-- =====================================================
-- إنشاء جدول ربط خصومات السلف بكشوف الرواتب
-- يوفر تتبع مفصل لجميع الخصومات التلقائية واليدوية
-- =====================================================

USE [TasksDB]
GO

PRINT '🚀 بدء إنشاء جدول ربط خصومات السلف بالرواتب...'

-- إنشاء جدول advance_payroll_deductions
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='advance_payroll_deductions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[advance_payroll_deductions] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [advance_id] INT NOT NULL,
        [payroll_id] INT NULL,
        [deduction_amount] DECIMAL(18,2) NOT NULL,
        [deduction_date] DATE NOT NULL,
        [is_automatic] BIT NOT NULL DEFAULT 1,
        [notes] NVARCHAR(500) NULL,
        [processed_by] INT NULL,
        [created_at] BIGINT NOT NULL,
        [updated_at] BIGINT NULL,
        
        CONSTRAINT [PK_advance_payroll_deductions] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_deductions_advance] FOREIGN KEY ([advance_id]) 
            REFERENCES [dbo].[employee_advances] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_deductions_payroll] FOREIGN KEY ([payroll_id]) 
            REFERENCES [dbo].[employee_payroll] ([id]) ON DELETE SET NULL,
        CONSTRAINT [FK_deductions_processor] FOREIGN KEY ([processed_by]) 
            REFERENCES [dbo].[users] ([id]) ON DELETE SET NULL,
        CONSTRAINT [CHK_deduction_amount_positive] CHECK ([deduction_amount] > 0)
    ) ON [PRIMARY]
    
    -- إنشاء فهارس لتحسين الأداء
    CREATE INDEX [IX_advance_payroll_deductions_advance_id] ON [dbo].[advance_payroll_deductions] ([advance_id]);
    CREATE INDEX [IX_advance_payroll_deductions_payroll_id] ON [dbo].[advance_payroll_deductions] ([payroll_id]);
    CREATE INDEX [IX_advance_payroll_deductions_date] ON [dbo].[advance_payroll_deductions] ([deduction_date]);
    CREATE INDEX [IX_advance_payroll_deductions_automatic] ON [dbo].[advance_payroll_deductions] ([is_automatic]);
    CREATE INDEX [IX_advance_payroll_deductions_month_year] ON [dbo].[advance_payroll_deductions] 
        (YEAR([deduction_date]), MONTH([deduction_date]));
    
    PRINT '✅ تم إنشاء جدول ربط خصومات السلف بالرواتب بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ جدول ربط خصومات السلف بالرواتب موجود مسبقاً'
END

-- إنشاء View لتسهيل الاستعلامات
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_advance_deductions_summary')
BEGIN
    EXEC('
    CREATE VIEW [dbo].[vw_advance_deductions_summary] AS
    SELECT 
        d.id,
        d.advance_id,
        d.payroll_id,
        d.deduction_amount,
        d.deduction_date,
        d.is_automatic,
        d.notes,
        
        -- معلومات السلفة
        a.employee_id,
        a.advance_type,
        a.amount as advance_total_amount,
        a.remaining_amount as advance_remaining_amount,
        a.monthly_deduction as advance_monthly_deduction,
        
        -- معلومات الموظف
        u.name as employee_name,
        u.full_name_arabic as employee_name_arabic,
        
        -- معلومات كشف الراتب (إن وجد)
        p.pay_period_start,
        p.pay_period_end,
        p.net_salary,
        
        -- معلومات المعالج
        processor.name as processed_by_name,
        
        -- حقول محسوبة
        YEAR(d.deduction_date) as deduction_year,
        MONTH(d.deduction_date) as deduction_month,
        CASE WHEN d.is_automatic = 1 THEN N''تلقائي'' ELSE N''يدوي'' END as deduction_type_arabic,
        FORMAT(d.deduction_amount, ''C'', ''ar-SA'') as formatted_amount
        
    FROM [dbo].[advance_payroll_deductions] d
    INNER JOIN [dbo].[employee_advances] a ON d.advance_id = a.id
    INNER JOIN [dbo].[users] u ON a.employee_id = u.id
    LEFT JOIN [dbo].[employee_payroll] p ON d.payroll_id = p.id
    LEFT JOIN [dbo].[users] processor ON d.processed_by = processor.id
    ')
    
    PRINT '✅ تم إنشاء View ملخص خصومات السلف'
END

-- إنشاء Stored Procedure للخصم التلقائي الشهري
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_ProcessMonthlyAdvanceDeductions')
BEGIN
    EXEC('
    CREATE PROCEDURE [dbo].[sp_ProcessMonthlyAdvanceDeductions]
        @ProcessDate DATE = NULL,
        @EmployeeId INT = NULL
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @CurrentDate DATE = ISNULL(@ProcessDate, GETDATE())
        DECLARE @ProcessedCount INT = 0
        DECLARE @TotalDeducted DECIMAL(18,2) = 0
        
        -- جدول مؤقت للنتائج
        CREATE TABLE #DeductionResults (
            AdvanceId INT,
            EmployeeId INT,
            DeductionAmount DECIMAL(18,2),
            RemainingAmount DECIMAL(18,2),
            Status NVARCHAR(50)
        )
        
        -- معالجة السلف المؤهلة للخصم
        INSERT INTO #DeductionResults
        SELECT 
            a.id as AdvanceId,
            a.employee_id as EmployeeId,
            CASE 
                WHEN a.monthly_deduction <= a.remaining_amount 
                THEN a.monthly_deduction 
                ELSE a.remaining_amount 
            END as DeductionAmount,
            a.remaining_amount - CASE 
                WHEN a.monthly_deduction <= a.remaining_amount 
                THEN a.monthly_deduction 
                ELSE a.remaining_amount 
            END as RemainingAmount,
            CASE 
                WHEN (a.remaining_amount - CASE 
                    WHEN a.monthly_deduction <= a.remaining_amount 
                    THEN a.monthly_deduction 
                    ELSE a.remaining_amount 
                END) <= 0 
                THEN ''completed'' 
                ELSE ''approved'' 
            END as Status
        FROM [dbo].[employee_advances] a
        WHERE a.status = ''approved''
          AND a.remaining_amount > 0
          AND a.monthly_deduction IS NOT NULL
          AND a.monthly_deduction > 0
          AND a.start_deduction_date IS NOT NULL
          AND a.start_deduction_date <= @CurrentDate
          AND (@EmployeeId IS NULL OR a.employee_id = @EmployeeId)
          AND NOT EXISTS (
              SELECT 1 FROM [dbo].[advance_payroll_deductions] d
              WHERE d.advance_id = a.id
                AND YEAR(d.deduction_date) = YEAR(@CurrentDate)
                AND MONTH(d.deduction_date) = MONTH(@CurrentDate)
          )
        
        -- تطبيق الخصومات
        DECLARE @AdvanceId INT, @EmployeeId INT, @DeductionAmount DECIMAL(18,2), @RemainingAmount DECIMAL(18,2), @NewStatus NVARCHAR(50)
        
        DECLARE deduction_cursor CURSOR FOR
        SELECT AdvanceId, EmployeeId, DeductionAmount, RemainingAmount, Status FROM #DeductionResults
        
        OPEN deduction_cursor
        FETCH NEXT FROM deduction_cursor INTO @AdvanceId, @EmployeeId, @DeductionAmount, @RemainingAmount, @NewStatus
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            -- تحديث السلفة
            UPDATE [dbo].[employee_advances]
            SET remaining_amount = @RemainingAmount,
                status = @NewStatus,
                updated_at = DATEDIFF(SECOND, ''1970-01-01'', GETUTCDATE())
            WHERE id = @AdvanceId
            
            -- إضافة سجل الخصم
            INSERT INTO [dbo].[advance_payroll_deductions]
            (advance_id, deduction_amount, deduction_date, is_automatic, notes, created_at)
            VALUES
            (@AdvanceId, @DeductionAmount, @CurrentDate, 1, N''خصم تلقائي شهري'', DATEDIFF(SECOND, ''1970-01-01'', GETUTCDATE()))
            
            SET @ProcessedCount = @ProcessedCount + 1
            SET @TotalDeducted = @TotalDeducted + @DeductionAmount
            
            FETCH NEXT FROM deduction_cursor INTO @AdvanceId, @EmployeeId, @DeductionAmount, @RemainingAmount, @NewStatus
        END
        
        CLOSE deduction_cursor
        DEALLOCATE deduction_cursor
        
        -- إرجاع النتائج
        SELECT 
            @ProcessedCount as ProcessedCount,
            @TotalDeducted as TotalDeducted,
            @CurrentDate as ProcessDate
        
        DROP TABLE #DeductionResults
    END
    ')
    
    PRINT '✅ تم إنشاء Stored Procedure للخصم التلقائي'
END

-- إنشاء Function لحساب إجمالي الخصومات لسلفة معينة
IF NOT EXISTS (SELECT * FROM sys.objects WHERE name = 'fn_GetAdvanceTotalDeductions' AND type = 'FN')
BEGIN
    EXEC('
    CREATE FUNCTION [dbo].[fn_GetAdvanceTotalDeductions](@AdvanceId INT)
    RETURNS DECIMAL(18,2)
    AS
    BEGIN
        DECLARE @TotalDeductions DECIMAL(18,2)
        
        SELECT @TotalDeductions = ISNULL(SUM(deduction_amount), 0)
        FROM [dbo].[advance_payroll_deductions]
        WHERE advance_id = @AdvanceId
        
        RETURN ISNULL(@TotalDeductions, 0)
    END
    ')
    
    PRINT '✅ تم إنشاء Function حساب إجمالي الخصومات'
END

PRINT '🎉 تم إنشاء جميع كائنات قاعدة البيانات لنظام خصومات السلف بنجاح!'
PRINT ''
PRINT '📊 الكائنات المُنشأة:'
PRINT '   ✓ جدول: advance_payroll_deductions'
PRINT '   ✓ فهارس: 5 فهارس لتحسين الأداء'
PRINT '   ✓ View: vw_advance_deductions_summary'
PRINT '   ✓ Stored Procedure: sp_ProcessMonthlyAdvanceDeductions'
PRINT '   ✓ Function: fn_GetAdvanceTotalDeductions'
PRINT ''
PRINT '🚀 النظام جاهز للاستخدام!'
