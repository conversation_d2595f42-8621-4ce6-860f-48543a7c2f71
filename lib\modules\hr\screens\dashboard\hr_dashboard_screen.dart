import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../routes/hr_routes.dart';


/// لوحة تحكم الموارد البشرية المتطورة والعصرية
class HRDashboardScreen extends StatefulWidget {
  const HRDashboardScreen({super.key});

  @override
  State<HRDashboardScreen> createState() => _HRDashboardScreenState();
}

class _HRDashboardScreenState extends State<HRDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // التأكد من تسجيل Controllers
    if (!Get.isRegistered<EmployeeController>()) {
      Get.lazyPut(() => EmployeeController());
    }
    if (!Get.isRegistered<AttendanceController>()) {
      Get.lazyPut(() => AttendanceController());
    }
    if (!Get.isRegistered<LeaveController>()) {
      Get.lazyPut(() => LeaveController());
    }
    if (!Get.isRegistered<PayrollController>()) {
      Get.lazyPut(() => PayrollController());
    }

    final employeeController = Get.find<EmployeeController>();
    final attendanceController = Get.find<AttendanceController>();
    final leaveController = Get.find<LeaveController>();
    final payrollController = Get.find<PayrollController>();

    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      employeeController.loadStatistics();
      attendanceController.loadTodayAttendance();
      leaveController.loadStatistics();
      payrollController.loadStatistics();
    });

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: CustomScrollView(
            slivers: [
              _buildModernAppBar(context, employeeController, attendanceController),
              _buildQuickStats(employeeController, attendanceController),
              _buildQuickActions(context),
              _buildMainContent(context, leaveController, attendanceController, payrollController),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildSmartFAB(context),
    );
  }

  /// بناء شريط التطبيق العصري مع تدرج لوني جميل
  Widget _buildModernAppBar(BuildContext context, EmployeeController employeeController, AttendanceController attendanceController) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade700,
                Colors.blue.shade500,
                Colors.cyan.shade400,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.business_center,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الموارد البشرية',
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withOpacity(0.3),
                                    offset: const Offset(0, 2),
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              'إدارة شاملة ومتطورة للموظفين',
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () async {
            await employeeController.loadStatistics();
            await attendanceController.loadTodayAttendance();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث البيانات بنجاح'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          icon: const Icon(Icons.refresh, color: Colors.white),
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          onPressed: () => _showNotifications(context),
          icon: Stack(
            children: [
              const Icon(Icons.notifications_outlined, color: Colors.white),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  child: const Text(
                    '3',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
          tooltip: 'الإشعارات',
        ),
        IconButton(
          onPressed: () => _showSettings(context),
          icon: const Icon(Icons.settings_outlined, color: Colors.white),
          tooltip: 'الإعدادات',
        ),
      ],
    );
  }

  /// بناء الإحصائيات السريعة بتصميم عصري
  Widget _buildQuickStats(EmployeeController employeeController, AttendanceController attendanceController) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // الصف الأول من الإحصائيات
            Row(
              children: [
                Expanded(
                  child: Obx(() => _buildStatCard(
                    title: 'إجمالي الموظفين',
                    value: employeeController.statistics.value?.totalEmployees.toString() ?? '0',
                    subtitle: 'موظف مسجل',
                    icon: Icons.people,
                    color: Colors.blue,
                    onTap: () => Get.toNamed(HRRoutes.employees),
                  )),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Obx(() => _buildStatCard(
                    title: 'الحضور اليوم',
                    value: attendanceController.todayPresentEmployees.length.toString(),
                    subtitle: 'موظف حاضر',
                    icon: Icons.access_time,
                    color: Colors.green,
                    onTap: () => Get.toNamed(HRRoutes.attendance),
                  )),
                ),
              ],
            ),

            // الصف الثاني من الإحصائيات
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Obx(() => _buildStatCard(
                    title: 'الموظفين النشطين',
                    value: employeeController.statistics.value?.activeEmployees.toString() ?? '0',
                    subtitle: 'موظف نشط',
                    icon: Icons.verified_user,
                    color: Colors.teal,
                    onTap: () => Get.toNamed(HRRoutes.employees),
                  )),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Obx(() => _buildStatCard(
                    title: 'التوظيف هذا الشهر',
                    value: employeeController.statistics.value?.newHiresThisMonth.toString() ?? '0',
                    subtitle: 'موظف جديد',
                    icon: Icons.person_add,
                    color: Colors.purple,
                    onTap: () => Get.toNamed(HRRoutes.employees),
                  )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية عصرية
  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
          border: Border.all(
            color: color.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإجراءات السريعة بتصميم عصري
  Widget _buildQuickActions(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإجراءات السريعة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildQuickActionCard(
                    title: 'إضافة موظف',
                    icon: Icons.person_add,
                    color: Colors.blue,
                    onTap: () => Get.toNamed(HRRoutes.addEmployee),
                  ),
                  _buildQuickActionCard(
                    title: 'طلب إجازة',
                    icon: Icons.event_available,
                    color: Colors.orange,
                    onTap: () => Get.toNamed(HRRoutes.addLeave),
                  ),
                  _buildQuickActionCard(
                    title: 'تسجيل حضور',
                    icon: Icons.fingerprint,
                    color: Colors.green,
                    onTap: () => Get.toNamed(HRRoutes.checkIn),
                  ),
                  _buildQuickActionCard(
                    title: 'كشف راتب',
                    icon: Icons.payment,
                    color: Colors.purple,
                    onTap: () => Get.toNamed(HRRoutes.addPayroll),
                  ),
                  _buildQuickActionCard(
                    title: 'التقارير',
                    icon: Icons.analytics,
                    color: Colors.teal,
                    onTap: () => Get.toNamed(HRRoutes.reports),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إجراء سريع
  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(left: 12),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 4),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent(BuildContext context, LeaveController leaveController, AttendanceController attendanceController, PayrollController payrollController) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نظرة عامة سريعة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 16),
            _buildOverviewGrid(context, leaveController, attendanceController, payrollController),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة النظرة العامة
  Widget _buildOverviewGrid(BuildContext context, LeaveController leaveController, AttendanceController attendanceController, PayrollController payrollController) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 1.2,
      children: [
        Obx(() => _buildOverviewCard(
          title: 'الحضور اليوم',
          value: attendanceController.todayPresentEmployees.length.toString(),
          icon: Icons.check_circle,
          color: Colors.green,
          onTap: () => Get.toNamed(HRRoutes.attendance),
        )),
        Obx(() => _buildOverviewCard(
          title: 'الغياب اليوم',
          value: attendanceController.todayAbsentEmployees.length.toString(),
          icon: Icons.cancel,
          color: Colors.red,
          onTap: () => Get.toNamed(HRRoutes.attendance),
        )),
        Obx(() => _buildOverviewCard(
          title: 'المتأخرين اليوم',
          value: attendanceController.todayLateEmployees.length.toString(),
          icon: Icons.schedule,
          color: Colors.orange,
          onTap: () => Get.toNamed(HRRoutes.attendance),
        )),
        Obx(() => _buildOverviewCard(
          title: 'إجمالي الإجازات',
          value: leaveController.statistics.value?.totalLeaves.toString() ?? '0',
          icon: Icons.event_available,
          color: Colors.blue,
          onTap: () => Get.toNamed(HRRoutes.leaves),
        )),
        Obx(() => _buildOverviewCard(
          title: 'الإجازات المعلقة',
          value: leaveController.statistics.value?.pendingLeaves.toString() ?? '0',
          icon: Icons.pending_actions,
          color: Colors.amber,
          onTap: () => Get.toNamed(HRRoutes.leaves),
        )),
        Obx(() => _buildOverviewCard(
          title: 'الرواتب المستحقة',
          value: payrollController.payrolls.length.toString(),
          icon: Icons.payment,
          color: Colors.purple,
          onTap: () => Get.toNamed(HRRoutes.payroll),
        )),
        _buildOverviewCard(
          title: 'السلف النشطة',
          value: '0', // سيتم تحديثها لاحقاً
          icon: Icons.account_balance_wallet,
          color: Colors.indigo,
          onTap: () => Get.toNamed(HRRoutes.advances),
        ),
        _buildOverviewCard(
          title: 'طلبات السلف',
          value: '0', // سيتم تحديثها لاحقاً
          icon: Icons.pending,
          color: Colors.orange,
          onTap: () => Get.toNamed(HRRoutes.advances),
        ),
      ],
    );
  }

  /// بناء بطاقة نظرة عامة
  Widget _buildOverviewCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإجراء الذكي
  Widget _buildSmartFAB(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickMenu(context),
      backgroundColor: Colors.blue.shade700,
      icon: const Icon(Icons.add, color: Colors.white),
      label: const Text(
        'إجراء سريع',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
    );
  }

  /// عرض الإشعارات
  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    'الإشعارات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            const Expanded(
              child: Center(
                child: Text('لا توجد إشعارات جديدة'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض الإعدادات
  void _showSettings(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    'الإعدادات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            const Expanded(
              child: Center(
                child: Text('إعدادات الموارد البشرية'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض القائمة السريعة
  void _showQuickMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر إجراءً سريعاً',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildQuickActionCard(
                  title: 'إضافة موظف',
                  icon: Icons.person_add,
                  color: Colors.blue,
                  onTap: () {
                    Navigator.pop(context);
                    Get.toNamed(HRRoutes.addEmployee);
                  },
                ),
                _buildQuickActionCard(
                  title: 'طلب إجازة',
                  icon: Icons.event_available,
                  color: Colors.orange,
                  onTap: () {
                    Navigator.pop(context);
                    Get.toNamed(HRRoutes.addLeave);
                  },
                ),
                _buildQuickActionCard(
                  title: 'تسجيل حضور',
                  icon: Icons.fingerprint,
                  color: Colors.green,
                  onTap: () {
                    Navigator.pop(context);
                    Get.toNamed(HRRoutes.checkIn);
                  },
                ),
                _buildQuickActionCard(
                  title: 'طلب سلفة',
                  icon: Icons.account_balance_wallet,
                  color: Colors.purple,
                  onTap: () {
                    Navigator.pop(context);
                    Get.toNamed(HRRoutes.addAdvance);
                  },
                ),
                _buildQuickActionCard(
                  title: 'إدارة السلف',
                  icon: Icons.monetization_on,
                  color: Colors.teal,
                  onTap: () {
                    Navigator.pop(context);
                    Get.toNamed(HRRoutes.advances);
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
