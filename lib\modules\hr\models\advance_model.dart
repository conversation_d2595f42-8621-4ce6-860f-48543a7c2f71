/// نموذج السلف والمساعدات المالية متوافق مع Backend API
class EmployeeAdvance {
  final int id;
  final int employeeId;
  final String advanceType; // salary_advance, financial_assistance, loan
  final double amount;
  final double remainingAmount;
  final double? monthlyDeduction;
  final DateTime requestDate;
  final DateTime? approvalDate;
  final DateTime? startDeductionDate;
  final String status; // pending, approved, rejected, completed
  final String? reason;
  final int? approvedBy;
  final String? notes;
  final int createdAt;
  final int? updatedAt;

  // Navigation Properties
  final String? employeeName;
  final String? employeeNameArabic;
  final String? approverName;

  const EmployeeAdvance({
    required this.id,
    required this.employeeId,
    required this.advanceType,
    required this.amount,
    required this.remainingAmount,
    this.monthlyDeduction,
    required this.requestDate,
    this.approvalDate,
    this.startDeductionDate,
    required this.status,
    this.reason,
    this.approvedBy,
    this.notes,
    required this.createdAt,
    this.updatedAt,
    this.employeeName,
    this.employeeNameArabic,
    this.approverName,
  });

  factory EmployeeAdvance.fromJson(Map<String, dynamic> json) {
    return EmployeeAdvance(
      id: json['id'] ?? 0,
      employeeId: json['employeeId'] ?? json['employee_id'] ?? 0,
      advanceType: json['advanceType'] ?? json['advance_type'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      remainingAmount: (json['remainingAmount'] ?? json['remaining_amount'] ?? 0).toDouble(),
      monthlyDeduction: json['monthlyDeduction'] != null || json['monthly_deduction'] != null
          ? (json['monthlyDeduction'] ?? json['monthly_deduction']).toDouble()
          : null,
      requestDate: DateTime.parse(json['requestDate'] ?? json['request_date'] ?? DateTime.now().toIso8601String()),
      approvalDate: json['approvalDate'] != null || json['approval_date'] != null
          ? DateTime.parse(json['approvalDate'] ?? json['approval_date'])
          : null,
      startDeductionDate: json['startDeductionDate'] != null || json['start_deduction_date'] != null
          ? DateTime.parse(json['startDeductionDate'] ?? json['start_deduction_date'])
          : null,
      status: json['status'] ?? 'pending',
      reason: json['reason'],
      approvedBy: json['approvedBy'] ?? json['approved_by'],
      notes: json['notes'],
      createdAt: json['createdAt'] ?? json['created_at'] ?? 0,
      updatedAt: json['updatedAt'] ?? json['updated_at'],
      employeeName: json['employeeName'] ?? json['employee_name'],
      employeeNameArabic: json['employeeNameArabic'] ?? json['employee_name_arabic'],
      approverName: json['approverName'] ?? json['approver_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'advanceType': advanceType,
      'amount': amount,
      'remainingAmount': remainingAmount,
      'monthlyDeduction': monthlyDeduction,
      'requestDate': requestDate.toIso8601String(),
      'approvalDate': approvalDate?.toIso8601String(),
      'startDeductionDate': startDeductionDate?.toIso8601String(),
      'status': status,
      'reason': reason,
      'approvedBy': approvedBy,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'employeeName': employeeName,
      'employeeNameArabic': employeeNameArabic,
      'approverName': approverName,
    };
  }

  /// نسخة محدثة من السلفة
  EmployeeAdvance copyWith({
    int? id,
    int? employeeId,
    String? advanceType,
    double? amount,
    double? remainingAmount,
    double? monthlyDeduction,
    DateTime? requestDate,
    DateTime? approvalDate,
    DateTime? startDeductionDate,
    String? status,
    String? reason,
    int? approvedBy,
    String? notes,
    int? createdAt,
    int? updatedAt,
    String? employeeName,
    String? employeeNameArabic,
    String? approverName,
  }) {
    return EmployeeAdvance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      advanceType: advanceType ?? this.advanceType,
      amount: amount ?? this.amount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      monthlyDeduction: monthlyDeduction ?? this.monthlyDeduction,
      requestDate: requestDate ?? this.requestDate,
      approvalDate: approvalDate ?? this.approvalDate,
      startDeductionDate: startDeductionDate ?? this.startDeductionDate,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      approvedBy: approvedBy ?? this.approvedBy,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      employeeName: employeeName ?? this.employeeName,
      employeeNameArabic: employeeNameArabic ?? this.employeeNameArabic,
      approverName: approverName ?? this.approverName,
    );
  }

  // Calculated Properties
  double get paidAmount => amount - remainingAmount;
  double get completionPercentage => amount > 0 ? ((amount - remainingAmount) / amount) * 100 : 0;
  bool get isCompleted => remainingAmount <= 0;
  int? get estimatedMonthsToComplete => monthlyDeduction != null && monthlyDeduction! > 0 && remainingAmount > 0 
      ? (remainingAmount / monthlyDeduction!).ceil() 
      : null;

  /// الحصول على نوع السلفة بالعربية
  String get advanceTypeArabic {
    switch (advanceType) {
      case 'salary_advance':
        return 'سلفة راتب';
      case 'financial_assistance':
        return 'مساعدة مالية';
      case 'loan':
        return 'قرض';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على حالة السلفة بالعربية
  String get statusArabic {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'approved':
        return 'موافق عليها';
      case 'rejected':
        return 'مرفوضة';
      case 'completed':
        return 'مكتملة';
      default:
        return 'غير محدد';
    }
  }

  /// لون حالة السلفة
  String get statusColor {
    switch (status) {
      case 'pending':
        return '#FF9800'; // برتقالي
      case 'approved':
        return '#4CAF50'; // أخضر
      case 'rejected':
        return '#F44336'; // أحمر
      case 'completed':
        return '#2196F3'; // أزرق
      default:
        return '#9E9E9E'; // رمادي
    }
  }
}

/// نموذج إنشاء سلفة جديدة
class CreateAdvanceDto {
  final int employeeId;
  final String advanceType;
  final double amount;
  final double? monthlyDeduction;
  final DateTime requestDate;
  final String? reason;
  final String? notes;

  const CreateAdvanceDto({
    required this.employeeId,
    required this.advanceType,
    required this.amount,
    this.monthlyDeduction,
    required this.requestDate,
    this.reason,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'employeeId': employeeId,
      'advanceType': advanceType,
      'amount': amount,
      'monthlyDeduction': monthlyDeduction,
      'requestDate': requestDate.toIso8601String(),
      'reason': reason,
      'notes': notes,
    };
  }
}

/// نموذج الموافقة على السلفة
class ApproveAdvanceDto {
  final bool isApproved; // true للموافقة، false للرفض
  final int approvedBy; // معرف المعتمد
  final DateTime? startDeductionDate;
  final String? rejectionReason;
  final String? notes;

  const ApproveAdvanceDto({
    required this.isApproved,
    required this.approvedBy,
    this.startDeductionDate,
    this.rejectionReason,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'isApproved': isApproved,
      'approvedBy': approvedBy,
      'startDeductionDate': startDeductionDate?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'notes': notes,
    };
  }
}

/// نموذج دفعة السلفة
class AdvancePaymentDto {
  final double paymentAmount;
  final DateTime paymentDate;
  final String? notes;

  const AdvancePaymentDto({
    required this.paymentAmount,
    required this.paymentDate,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'paymentAmount': paymentAmount,
      'paymentDate': paymentDate.toIso8601String(),
      'notes': notes,
    };
  }
}

/// نموذج إحصائيات السلف
class AdvanceStatisticsDto {
  final int totalAdvances;
  final int pendingAdvances;
  final int approvedAdvances;
  final int rejectedAdvances;
  final int completedAdvances;
  final double totalAmount;
  final double totalRemainingAmount;
  final double totalPaidAmount;
  final Map<String, int> advancesByType;
  final Map<String, double> amountsByType;

  const AdvanceStatisticsDto({
    required this.totalAdvances,
    required this.pendingAdvances,
    required this.approvedAdvances,
    required this.rejectedAdvances,
    required this.completedAdvances,
    required this.totalAmount,
    required this.totalRemainingAmount,
    required this.totalPaidAmount,
    required this.advancesByType,
    required this.amountsByType,
  });

  factory AdvanceStatisticsDto.fromJson(Map<String, dynamic> json) {
    return AdvanceStatisticsDto(
      totalAdvances: json['totalAdvances'] ?? 0,
      pendingAdvances: json['pendingAdvances'] ?? 0,
      approvedAdvances: json['approvedAdvances'] ?? 0,
      rejectedAdvances: json['rejectedAdvances'] ?? 0,
      completedAdvances: json['completedAdvances'] ?? 0,
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      totalRemainingAmount: (json['totalRemainingAmount'] ?? 0).toDouble(),
      totalPaidAmount: (json['totalPaidAmount'] ?? 0).toDouble(),
      advancesByType: Map<String, int>.from(json['advancesByType'] ?? {}),
      amountsByType: Map<String, double>.from(
        (json['amountsByType'] ?? {}).map((key, value) => MapEntry(key, value.toDouble()))
      ),
    );
  }
}
