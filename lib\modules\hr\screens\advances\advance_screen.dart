import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

import '../../controllers/advance_controller.dart';
import '../../models/advance_model.dart';
import '../../../../services/unified_permission_service.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';
import '../../../../widgets/error_message.dart';
import '../../../../widgets/empty_state.dart';
import '../../../hr/routes/hr_routes.dart';

/// شاشة إدارة السلف والمساعدات المالية
class AdvanceScreen extends StatefulWidget {
  const AdvanceScreen({super.key});

  @override
  State<AdvanceScreen> createState() => _AdvanceScreenState();
}

class _AdvanceScreenState extends State<AdvanceScreen> {
  final AdvanceController controller = Get.put(AdvanceController());
  final UnifiedPermissionService permissionService = Get.find<UnifiedPermissionService>();

  late AdvanceDataSource _advanceDataSource;
  final DataGridController _dataGridController = DataGridController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _advanceDataSource = AdvanceDataSource(controller.advances);

    // ربط البيانات مع الجدول
    ever(controller.advances, (List<EmployeeAdvance> advances) {
      _advanceDataSource.updateData(advances);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة السلف والمساعدات المالية'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // if (permissionService.canCreateAdvance())
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _navigateToAddAdvance(),
              tooltip: 'إضافة سلفة جديدة',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadAdvances(refresh: true),
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'statistics',
                child: ListTile(
                  leading: Icon(Icons.analytics),
                  title: Text('الإحصائيات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('تصدير التقرير'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'filters',
                child: ListTile(
                  leading: Icon(Icons.filter_list),
                  title: Text('الفلاتر المتقدمة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلترة
          _buildSearchAndFilterBar(),

          // إحصائيات سريعة
          _buildQuickStats(),

          // الجدول
          Expanded(
            child: _buildAdvancesTable(),
          ),
        ],
      ),
      floatingActionButton: // permissionService.canCreateAdvance() ?
          FloatingActionButton(
              onPressed: () => _navigateToAddAdvance(),
              tooltip: 'إضافة سلفة جديدة',
              child: const Icon(Icons.add),
            ),
          // : null,
    );
  }

  /// بناء شريط البحث والفلترة
  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في السلف...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              controller.searchAdvances('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  onChanged: (value) {
                    controller.searchAdvances(value);
                  },
                ),
              ),
              const SizedBox(width: 12),
              // زر الفلاتر
              OutlinedButton.icon(
                onPressed: () => _showFiltersDialog(),
                icon: const Icon(Icons.filter_list),
                label: const Text('فلترة'),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // فلاتر سريعة
          Obx(() => SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildQuickFilter('الكل', '', controller.selectedStatus.value),
                _buildQuickFilter('في الانتظار', 'pending', controller.selectedStatus.value),
                _buildQuickFilter('موافق عليها', 'approved', controller.selectedStatus.value),
                _buildQuickFilter('مرفوضة', 'rejected', controller.selectedStatus.value),
                _buildQuickFilter('مكتملة', 'completed', controller.selectedStatus.value),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// بناء فلتر سريع
  Widget _buildQuickFilter(String label, String value, String selectedValue) {
    final isSelected = selectedValue == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          controller.filterByStatus(selected ? value : '');
        },
        backgroundColor: Colors.grey.shade100,
        selectedColor: AppColors.primary.withOpacity(0.2),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Obx(() {
      final stats = controller.statistics.value;
      if (stats == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي السلف',
                stats.totalAdvances.toString(),
                Icons.account_balance_wallet,
                AppColors.primary,
              ),
            ),
            Expanded(
              child: _buildStatCard(
                'في الانتظار',
                stats.pendingAdvances.toString(),
                Icons.pending,
                Colors.orange,
              ),
            ),
            Expanded(
              child: _buildStatCard(
                'موافق عليها',
                stats.approvedAdvances.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
            Expanded(
              child: _buildStatCard(
                'المبلغ الإجمالي',
                '${stats.totalAmount.toStringAsFixed(0)} ريال يمني',
                Icons.monetization_on,
                Colors.blue,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء جدول السلف
  Widget _buildAdvancesTable() {
    return Obx(() {
      if (controller.isLoading.value && controller.advances.isEmpty) {
        return const LoadingIndicator();
      }

      if (controller.error.value.isNotEmpty && controller.advances.isEmpty) {
        return ErrorMessage(
          message: controller.error.value,
          onRetry: () => controller.loadAdvances(refresh: true),
        );
      }

      if (controller.advances.isEmpty) {
        return const EmptyState(
          icon: Icons.account_balance_wallet,
          title: 'لا توجد سلف',
          message: 'لم يتم العثور على أي سلف أو مساعدات مالية',
        );
      }

      return Column(
        children: [
          Expanded(
            child: SfDataGrid(
              source: _advanceDataSource,
              controller: _dataGridController,
              allowSorting: true,
              allowFiltering: true,
              allowColumnsResizing: true,
              columnResizeMode: ColumnResizeMode.onResize,
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              selectionMode: SelectionMode.single,
              navigationMode: GridNavigationMode.cell,
              columnWidthMode: ColumnWidthMode.auto,
              columns: _buildColumns(),
              onCellTap: (details) {
                if (details.rowColumnIndex.rowIndex > 0) {
                  final advance = controller.advances[details.rowColumnIndex.rowIndex - 1];
                  _navigateToAdvanceDetails(advance);
                }
              },
            ),
          ),

          // مؤشر التحميل للمزيد
          Obx(() => controller.isLoadingMore.value
              ? const Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                )
              : const SizedBox.shrink()),
        ],
      );
    });
  }

  /// بناء أعمدة الجدول
  List<GridColumn> _buildColumns() {
    return [
      GridColumn(
        columnName: 'employeeName',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('اسم الموظف', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 150,
      ),
      GridColumn(
        columnName: 'advanceType',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('نوع السلفة', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'amount',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('المبلغ', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'remainingAmount',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('المتبقي', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'status',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('الحالة', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 100,
      ),
      GridColumn(
        columnName: 'requestDate',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('تاريخ الطلب', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 120,
      ),
      GridColumn(
        columnName: 'actions',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.center,
          child: Text('الإجراءات', style: AppStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
        ),
        width: 120,
        allowSorting: false,
        allowFiltering: false,
      ),
    ];
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    // TODO: تنفيذ حوار الفلاتر المتقدمة
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'statistics':
        _showStatisticsDialog();
        break;
      case 'export':
        _exportReport();
        break;
      case 'filters':
        _showFiltersDialog();
        break;
    }
  }

  /// عرض حوار الإحصائيات
  void _showStatisticsDialog() {
    // TODO: تنفيذ حوار الإحصائيات المفصلة
  }

  /// تصدير التقرير
  void _exportReport() {
    // TODO: تنفيذ تصدير التقرير
  }

  /// الانتقال لإضافة سلفة جديدة
  void _navigateToAddAdvance() {
    Get.toNamed(HRRoutes.addAdvance);
  }

  /// الانتقال لتفاصيل السلفة
  void _navigateToAdvanceDetails(EmployeeAdvance advance) {
    controller.selectAdvance(advance);
    Get.toNamed(HRRoutes.advanceDetails.replaceAll(':id', advance.id.toString()));
  }
}

/// مصدر بيانات السلف للجدول
class AdvanceDataSource extends DataGridSource {
  List<EmployeeAdvance> _advances = [];

  AdvanceDataSource(List<EmployeeAdvance> advances) {
    updateData(advances);
  }

  void updateData(List<EmployeeAdvance> advances) {
    _advances = advances;
    notifyListeners();
  }

  @override
  List<DataGridRow> get rows => _advances.map<DataGridRow>((advance) {
    return DataGridRow(cells: [
      DataGridCell<String>(
        columnName: 'employeeName',
        value: advance.employeeNameArabic ?? advance.employeeName ?? 'غير محدد',
      ),
      DataGridCell<String>(
        columnName: 'advanceType',
        value: advance.advanceTypeArabic,
      ),
      DataGridCell<String>(
        columnName: 'amount',
        value: '${advance.amount.toStringAsFixed(0)} ريال يمني',
      ),
      DataGridCell<String>(
        columnName: 'remainingAmount',
        value: '${advance.remainingAmount.toStringAsFixed(0)} ريال يمني',
      ),
      DataGridCell<Widget>(
        columnName: 'status',
        value: _buildStatusChip(advance.status, advance.statusArabic),
      ),
      DataGridCell<String>(
        columnName: 'requestDate',
        value: _formatDate(advance.requestDate),
      ),
      DataGridCell<Widget>(
        columnName: 'actions',
        value: _buildActionButtons(advance),
      ),
    ]);
  }).toList();

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        if (cell.value is Widget) {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8),
            child: cell.value,
          );
        }
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.all(8),
          child: Text(
            cell.value?.toString() ?? '',
            style: const TextStyle(fontSize: 13),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatusChip(String status, String statusArabic) {
    Color color;
    switch (status) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'approved':
        color = Colors.green;
        break;
      case 'rejected':
        color = Colors.red;
        break;
      case 'completed':
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        statusArabic,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildActionButtons(EmployeeAdvance advance) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.visibility, size: 18),
          onPressed: () => _viewAdvance(advance),
          tooltip: 'عرض التفاصيل',
        ),
        if (advance.status == 'pending')
          IconButton(
            icon: const Icon(Icons.edit, size: 18),
            onPressed: () => _editAdvance(advance),
            tooltip: 'تعديل',
          ),
      ],
    );
  }

  void _viewAdvance(EmployeeAdvance advance) {
    Get.find<AdvanceController>().selectAdvance(advance);
    Get.toNamed(HRRoutes.advanceDetails.replaceAll(':id', advance.id.toString()));
  }

  void _editAdvance(EmployeeAdvance advance) {
    Get.toNamed(HRRoutes.editAdvance.replaceAll(':id', advance.id.toString()));
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
