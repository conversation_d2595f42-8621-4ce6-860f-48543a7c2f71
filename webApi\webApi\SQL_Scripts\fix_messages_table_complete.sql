-- إصلاح شامل لجدول messages لجعله متطابقاً مع النموذج
-- هذا السكريبت يضيف جميع الأعمدة المفقودة ويحسن الأداء

USE [TaskManagementDB]
GO

PRINT '🔧 === بدء الإصلاح الشامل لجدول messages ==='

-- 1. التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'messages')
BEGIN
    PRINT '❌ جدول messages غير موجود - يجب إنشاؤه أولاً'
    RETURN
END

PRINT '✅ جدول messages موجود - بدء إضافة الأعمدة المفقودة...'

-- 2. إضافة عمود content_type إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'content_type')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [content_type] INT NOT NULL DEFAULT 0
    PRINT '✅ تم إضافة عمود content_type'
END
ELSE
    PRINT '⚠️ عمود content_type موجود بالفعل'

-- 3. إضافة عمود priority إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'priority')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [priority] INT NOT NULL DEFAULT 0
    PRINT '✅ تم إضافة عمود priority'
END
ELSE
    PRINT '⚠️ عمود priority موجود بالفعل'

-- 4. إضافة عمود is_read إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'is_read')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [is_read] BIT NOT NULL DEFAULT 0
    PRINT '✅ تم إضافة عمود is_read'
END
ELSE
    PRINT '⚠️ عمود is_read موجود بالفعل'

-- 5. إضافة عمود is_marked_for_follow_up إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'is_marked_for_follow_up')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [is_marked_for_follow_up] BIT NOT NULL DEFAULT 0
    PRINT '✅ تم إضافة عمود is_marked_for_follow_up'
END
ELSE
    PRINT '⚠️ عمود is_marked_for_follow_up موجود بالفعل'

-- 6. إضافة عمود follow_up_at إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'follow_up_at')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [follow_up_at] BIGINT NULL
    PRINT '✅ تم إضافة عمود follow_up_at'
END
ELSE
    PRINT '⚠️ عمود follow_up_at موجود بالفعل'

-- 7. إضافة عمود marked_for_follow_up_by إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'marked_for_follow_up_by')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [marked_for_follow_up_by] INT NULL
    PRINT '✅ تم إضافة عمود marked_for_follow_up_by'
END
ELSE
    PRINT '⚠️ عمود marked_for_follow_up_by موجود بالفعل'

-- 8. إضافة عمود is_edited إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'is_edited')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [is_edited] BIT NOT NULL DEFAULT 0
    PRINT '✅ تم إضافة عمود is_edited'
END
ELSE
    PRINT '⚠️ عمود is_edited موجود بالفعل'

-- 9. إضافة عمود receiver_id إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'receiver_id')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [receiver_id] INT NULL
    PRINT '✅ تم إضافة عمود receiver_id'
END
ELSE
    PRINT '⚠️ عمود receiver_id موجود بالفعل'

-- 10. إضافة عمود sent_at إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.messages') AND name = 'sent_at')
BEGIN
    ALTER TABLE [dbo].[messages] ADD [sent_at] BIGINT NULL
    PRINT '✅ تم إضافة عمود sent_at'
END
ELSE
    PRINT '⚠️ عمود sent_at موجود بالفعل'

-- 11. إضافة المفاتيح الخارجية المفقودة
PRINT '🔗 إضافة المفاتيح الخارجية...'

-- مفتاح خارجي لـ marked_for_follow_up_by
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_messages_marked_for_follow_up_by')
BEGIN
    ALTER TABLE [dbo].[messages]
    ADD CONSTRAINT [FK_messages_marked_for_follow_up_by] 
    FOREIGN KEY ([marked_for_follow_up_by]) REFERENCES [dbo].[users] ([id])
    PRINT '✅ تم إضافة مفتاح خارجي لـ marked_for_follow_up_by'
END

-- مفتاح خارجي لـ receiver_id
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_messages_receiver_id')
BEGIN
    ALTER TABLE [dbo].[messages]
    ADD CONSTRAINT [FK_messages_receiver_id] 
    FOREIGN KEY ([receiver_id]) REFERENCES [dbo].[users] ([id])
    PRINT '✅ تم إضافة مفتاح خارجي لـ receiver_id'
END

-- 12. إنشاء فهارس لتحسين الأداء
PRINT '📊 إنشاء فهارس لتحسين الأداء...'

-- فهرس لـ content_type
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_messages_content_type')
BEGIN
    CREATE INDEX [IX_messages_content_type] ON [dbo].[messages] ([content_type])
    PRINT '✅ تم إنشاء فهرس IX_messages_content_type'
END

-- فهرس لـ priority
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_messages_priority')
BEGIN
    CREATE INDEX [IX_messages_priority] ON [dbo].[messages] ([priority])
    PRINT '✅ تم إنشاء فهرس IX_messages_priority'
END

-- فهرس لـ is_read
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_messages_is_read')
BEGIN
    CREATE INDEX [IX_messages_is_read] ON [dbo].[messages] ([is_read])
    PRINT '✅ تم إنشاء فهرس IX_messages_is_read'
END

-- فهرس لـ is_marked_for_follow_up
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_messages_is_marked_for_follow_up')
BEGIN
    CREATE INDEX [IX_messages_is_marked_for_follow_up] ON [dbo].[messages] ([is_marked_for_follow_up])
    PRINT '✅ تم إنشاء فهرس IX_messages_is_marked_for_follow_up'
END

-- فهرس مركب للبحث السريع
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_messages_group_sender_created')
BEGIN
    CREATE INDEX [IX_messages_group_sender_created] ON [dbo].[messages] ([GroupId], [SenderId], [CreatedAt])
    PRINT '✅ تم إنشاء فهرس مركب IX_messages_group_sender_created'
END

-- 13. إضافة قيود التحقق
PRINT '🔒 إضافة قيود التحقق...'

-- قيد للتحقق من content_type
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_messages_content_type')
BEGIN
    ALTER TABLE [dbo].[messages]
    ADD CONSTRAINT [CK_messages_content_type] 
    CHECK ([content_type] >= 0 AND [content_type] <= 10)
    PRINT '✅ تم إضافة قيد التحقق CK_messages_content_type'
END

-- قيد للتحقق من priority
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_messages_priority')
BEGIN
    ALTER TABLE [dbo].[messages]
    ADD CONSTRAINT [CK_messages_priority] 
    CHECK ([priority] >= 0 AND [priority] <= 2)
    PRINT '✅ تم إضافة قيد التحقق CK_messages_priority'
END

-- 14. تحديث البيانات الموجودة
PRINT '🔄 تحديث البيانات الموجودة...'

-- تحديث sent_at للرسائل الموجودة
UPDATE [dbo].[messages] 
SET [sent_at] = [CreatedAt] 
WHERE [sent_at] IS NULL

PRINT '✅ تم تحديث sent_at للرسائل الموجودة'

-- 15. عرض ملخص الجدول النهائي
PRINT '📋 === ملخص بنية الجدول النهائية ==='
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'messages'
ORDER BY ORDINAL_POSITION

PRINT '✅ === تم الانتهاء من الإصلاح الشامل لجدول messages ==='
PRINT 'الجدول الآن متطابق تماماً مع نموذج Message.cs'

-- 16. إنشاء view للرسائل مع التفاصيل الكاملة
PRINT '👁️ إنشاء view للرسائل مع التفاصيل...'

IF OBJECT_ID('dbo.vw_messages_detailed', 'V') IS NOT NULL
    DROP VIEW [dbo].[vw_messages_detailed]

CREATE VIEW [dbo].[vw_messages_detailed] AS
SELECT
    m.*,
    s.name AS sender_name,
    s.email AS sender_email,
    r.name AS receiver_name,
    cg.name AS group_name,
    cg.group_type,
    mfb.name AS marked_for_follow_up_by_name,
    pb.name AS pinned_by_name,
    (SELECT COUNT(*) FROM message_reads mr WHERE mr.message_id = m.id) AS read_count,
    (SELECT COUNT(*) FROM message_reactions mreact WHERE mreact.message_id = m.id AND mreact.is_deleted = 0) AS reaction_count
FROM messages m
LEFT JOIN users s ON m.SenderId = s.id
LEFT JOIN users r ON m.receiver_id = r.id
LEFT JOIN chat_groups cg ON m.GroupId = cg.id
LEFT JOIN users mfb ON m.marked_for_follow_up_by = mfb.id
LEFT JOIN users pb ON m.PinnedBy = pb.id
WHERE m.IsDeleted = 0

PRINT '✅ تم إنشاء view vw_messages_detailed'

-- 17. إنشاء stored procedure للبحث في الرسائل
PRINT '🔍 إنشاء stored procedure للبحث...'

IF OBJECT_ID('dbo.sp_search_messages', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[sp_search_messages]

CREATE PROCEDURE [dbo].[sp_search_messages]
    @SearchTerm NVARCHAR(255) = NULL,
    @GroupId INT = NULL,
    @SenderId INT = NULL,
    @ContentType INT = NULL,
    @Priority INT = NULL,
    @IsRead BIT = NULL,
    @DateFrom BIGINT = NULL,
    @DateTo BIGINT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    SELECT
        m.*,
        s.name AS sender_name,
        cg.name AS group_name
    FROM messages m
    LEFT JOIN users s ON m.SenderId = s.id
    LEFT JOIN chat_groups cg ON m.GroupId = cg.id
    WHERE m.IsDeleted = 0
        AND (@SearchTerm IS NULL OR m.Content LIKE '%' + @SearchTerm + '%')
        AND (@GroupId IS NULL OR m.GroupId = @GroupId)
        AND (@SenderId IS NULL OR m.SenderId = @SenderId)
        AND (@ContentType IS NULL OR m.content_type = @ContentType)
        AND (@Priority IS NULL OR m.priority = @Priority)
        AND (@IsRead IS NULL OR m.is_read = @IsRead)
        AND (@DateFrom IS NULL OR m.CreatedAt >= @DateFrom)
        AND (@DateTo IS NULL OR m.CreatedAt <= @DateTo)
    ORDER BY m.CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;

    -- إرجاع العدد الإجمالي
    SELECT COUNT(*) AS TotalCount
    FROM messages m
    WHERE m.IsDeleted = 0
        AND (@SearchTerm IS NULL OR m.Content LIKE '%' + @SearchTerm + '%')
        AND (@GroupId IS NULL OR m.GroupId = @GroupId)
        AND (@SenderId IS NULL OR m.SenderId = @SenderId)
        AND (@ContentType IS NULL OR m.content_type = @ContentType)
        AND (@Priority IS NULL OR m.priority = @Priority)
        AND (@IsRead IS NULL OR m.is_read = @IsRead)
        AND (@DateFrom IS NULL OR m.CreatedAt >= @DateFrom)
        AND (@DateTo IS NULL OR m.CreatedAt <= @DateTo);
END

PRINT '✅ تم إنشاء stored procedure sp_search_messages'

PRINT '🎉 === الإصلاح الشامل مكتمل بنجاح ==='
