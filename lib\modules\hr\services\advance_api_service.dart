import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../../../services/api/api_service.dart';
import '../../../helpers/user_helper.dart';
import '../models/advance_model.dart';

/// خدمة API للسلف والمساعدات المالية
class AdvanceApiService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// الحصول على جميع السلف مع الفلترة والترقيم
  Future<Map<String, dynamic>> getAllAdvances({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? advanceType,
    int? departmentId,
    String? searchTerm,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'pageSize': pageSize,
      };

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }
      if (advanceType != null && advanceType.isNotEmpty) {
        queryParams['advanceType'] = advanceType;
      }
      if (departmentId != null) {
        queryParams['departmentId'] = departmentId;
      }
      if (searchTerm != null && searchTerm.isNotEmpty) {
        queryParams['searchTerm'] = searchTerm;
      }

      final response = await _apiService.get(
        '/api/EmployeeAdvance',
        queryParams: queryParams,
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );

      // تحويل البيانات إلى نماذج
      final advances = (data['advances'] as List?)
          ?.map((json) => EmployeeAdvance.fromJson(json))
          .toList() ?? [];

      return {
        'advances': advances,
        'totalRecords': data['totalRecords'] ?? 0,
        'totalPages': data['totalPages'] ?? 0,
        'currentPage': data['currentPage'] ?? 1,
        'pageSize': data['pageSize'] ?? pageSize,
      };
    } catch (e) {
      debugPrint('❌ خطأ في تحميل السلف: $e');
      rethrow;
    }
  }

  /// الحصول على سلف موظف محدد
  Future<List<EmployeeAdvance>> getEmployeeAdvances(
    int employeeId, {
    String? status,
    String? advanceType,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }
      if (advanceType != null && advanceType.isNotEmpty) {
        queryParams['advanceType'] = advanceType;
      }

      final response = await _apiService.get(
        '/api/EmployeeAdvance/employee/$employeeId',
        queryParams: queryParams,
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<List<dynamic>>(
        response,
        (json) => json as List<dynamic>,
      );

      return data.map((json) => EmployeeAdvance.fromJson(json)).toList();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سلف الموظف: $e');
      rethrow;
    }
  }

  /// الحصول على سلفة محددة
  Future<EmployeeAdvance> getAdvance(int id) async {
    try {
      final response = await _apiService.get(
        '/api/EmployeeAdvance/$id',
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );

      return EmployeeAdvance.fromJson(data);
    } catch (e) {
      debugPrint('❌ خطأ في تحميل السلفة: $e');
      rethrow;
    }
  }

  /// إنشاء سلفة جديدة
  Future<EmployeeAdvance> createAdvance(CreateAdvanceDto dto) async {
    try {
      final response = await _apiService.post(
        '/api/EmployeeAdvance',
        dto.toJson(),
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );

      return EmployeeAdvance.fromJson(data);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء السلفة: $e');
      rethrow;
    }
  }

  /// الموافقة على سلفة أو رفضها
  Future<void> approveAdvance(int id, ApproveAdvanceDto dto) async {
    try {
      final response = await _apiService.post(
        '/api/EmployeeAdvance/$id/approve',
        dto.toJson(),
        headers: UserHelper.getAuthHeaders(),
      );

      _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ خطأ في معالجة طلب السلفة: $e');
      rethrow;
    }
  }

  /// تسجيل دفعة للسلفة
  Future<Map<String, dynamic>> recordPayment(int id, AdvancePaymentDto dto) async {
    try {
      final response = await _apiService.post(
        '/api/EmployeeAdvance/$id/payment',
        dto.toJson(),
        headers: UserHelper.getAuthHeaders(),
      );

      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل الدفعة: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات السلف
  Future<AdvanceStatisticsDto> getAdvanceStatistics() async {
    try {
      final response = await _apiService.get(
        '/api/EmployeeAdvance/statistics',
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );

      return AdvanceStatisticsDto.fromJson(data);
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إحصائيات السلف: $e');
      rethrow;
    }
  }

  /// حذف سلفة (إذا كانت في حالة pending)
  Future<void> deleteAdvance(int id) async {
    try {
      final response = await _apiService.delete(
        '/api/EmployeeAdvance/$id',
        headers: UserHelper.getAuthHeaders(),
      );

      _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ خطأ في حذف السلفة: $e');
      rethrow;
    }
  }

  /// البحث في السلف
  Future<List<EmployeeAdvance>> searchAdvances(String searchTerm) async {
    try {
      final response = await _apiService.get(
        '/api/EmployeeAdvance/search',
        queryParams: {'searchTerm': searchTerm},
        headers: UserHelper.getAuthHeaders(),
      );

      final data = _apiService.handleResponse<List<dynamic>>(
        response,
        (json) => json as List<dynamic>,
      );

      return data.map((json) => EmployeeAdvance.fromJson(json)).toList();
    } catch (e) {
      debugPrint('❌ خطأ في البحث في السلف: $e');
      rethrow;
    }
  }

  /// الحصول على أنواع السلف المتاحة
  List<Map<String, String>> getAdvanceTypes() {
    return [
      {
        'value': 'salary_advance',
        'label': 'سلفة راتب',
        'description': 'سلفة من الراتب الشهري',
      },
      {
        'value': 'financial_assistance',
        'label': 'مساعدة مالية',
        'description': 'مساعدة مالية للظروف الطارئة',
      },
      {
        'value': 'loan',
        'label': 'قرض',
        'description': 'قرض طويل المدى',
      },
    ];
  }

  /// الحصول على حالات السلف المتاحة
  List<Map<String, String>> getAdvanceStatuses() {
    return [
      {
        'value': 'pending',
        'label': 'في الانتظار',
        'color': '#FF9800',
      },
      {
        'value': 'approved',
        'label': 'موافق عليها',
        'color': '#4CAF50',
      },
      {
        'value': 'rejected',
        'label': 'مرفوضة',
        'color': '#F44336',
      },
      {
        'value': 'completed',
        'label': 'مكتملة',
        'color': '#2196F3',
      },
    ];
  }

  /// التحقق من إمكانية طلب سلفة جديدة للموظف
  Future<Map<String, dynamic>> checkAdvanceEligibility(int employeeId) async {
    try {
      final response = await _apiService.get(
        '/api/EmployeeAdvance/employee/$employeeId/eligibility',
        headers: UserHelper.getAuthHeaders(),
      );

      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من أهلية السلفة: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير السلف لفترة محددة
  Future<Map<String, dynamic>> getAdvanceReport({
    DateTime? fromDate,
    DateTime? toDate,
    int? departmentId,
    String? advanceType,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.toIso8601String();
      }
      if (toDate != null) {
        queryParams['toDate'] = toDate.toIso8601String();
      }
      if (departmentId != null) {
        queryParams['departmentId'] = departmentId;
      }
      if (advanceType != null && advanceType.isNotEmpty) {
        queryParams['advanceType'] = advanceType;
      }
      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      final response = await _apiService.get(
        '/api/EmployeeAdvance/report',
        queryParams: queryParams,
        headers: UserHelper.getAuthHeaders(),
      );

      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل تقرير السلف: $e');
      rethrow;
    }
  }
}
