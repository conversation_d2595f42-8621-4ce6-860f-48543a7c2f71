import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../controllers/advance_controller.dart';
import '../../controllers/employee_controller.dart';
import '../../services/employee_api_service.dart';
import '../../models/advance_model.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';
// import '../../../../widgets/custom_text_field.dart';
// import '../../../../widgets/custom_dropdown.dart';
// import '../../../../widgets/custom_date_picker.dart';
import '../../../../helpers/user_helper.dart';

/// شاشة إضافة سلفة جديدة
class AddAdvanceScreen extends StatefulWidget {
  const AddAdvanceScreen({super.key});

  @override
  State<AddAdvanceScreen> createState() => _AddAdvanceScreenState();
}

class _AddAdvanceScreenState extends State<AddAdvanceScreen> {
  final AdvanceController controller = Get.find<AdvanceController>();
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _amountController = TextEditingController();
  final _monthlyDeductionController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  // المتغيرات
  int? _selectedEmployeeId;
  String? _selectedAdvanceType;
  DateTime _requestDate = DateTime.now();
  bool _isLoading = false;

  // قائمة الموظفين (سيتم تحميلها من API)
  final RxList<Map<String, dynamic>> employees = <Map<String, dynamic>>[].obs;

  // وضع التعديل
  bool _isEditMode = false;
  int? _editingAdvanceId;

  @override
  void initState() {
    super.initState();
    _loadEmployees();

    // التحقق من وضع التعديل
    final advanceId = Get.arguments as int?;
    if (advanceId != null) {
      _isEditMode = true;
      _loadAdvanceForEdit(advanceId);
    } else {
      // إذا كان المستخدم الحالي موظف، اختره تلقائياً
      final currentUser = UserHelper.getCurrentUser();
      if (currentUser != null) {
        _selectedEmployeeId = currentUser.id;
      }
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _monthlyDeductionController.dispose();
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الموظفين من قاعدة البيانات
  Future<void> _loadEmployees() async {
    try {
      // التأكد من تسجيل EmployeeApiService و EmployeeController
      if (!Get.isRegistered<EmployeeApiService>()) {
        Get.lazyPut(() => EmployeeApiService());
      }
      if (!Get.isRegistered<EmployeeController>()) {
        Get.lazyPut(() => EmployeeController());
      }

      final employeeController = Get.find<EmployeeController>();
      await employeeController.loadEmployees();

      // تحويل بيانات الموظفين للتنسيق المطلوب مع إزالة المكررات
      final uniqueEmployees = <int, Map<String, dynamic>>{};

      for (final employee in employeeController.employees) {
        uniqueEmployees[employee.id] = {
          'id': employee.id,
          'name': employee.fullNameArabic ?? employee.name,
          'department': employee.departmentName ?? 'غير محدد',
          'employeeId': employee.employeeId ?? '',
          'jobTitle': employee.jobTitle ?? '',
        };
      }

      employees.value = uniqueEmployees.values.toList();

      // التحقق من صحة الموظف المختار
      if (_selectedEmployeeId != null) {
        final employeeExists =
            employees.any((emp) => emp['id'] == _selectedEmployeeId);
        if (!employeeExists) {
          setState(() {
            _selectedEmployeeId = null;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الموظفين: $e');
      Get.snackbar('خطأ', 'فشل في تحميل قائمة الموظفين');
    }
  }

  /// تحميل بيانات السلفة للتعديل
  Future<void> _loadAdvanceForEdit(int advanceId) async {
    try {
      _editingAdvanceId = advanceId;
      final advance = await controller.getAdvanceById(advanceId);
      if (advance != null) {
        setState(() {
          _selectedEmployeeId = advance.employeeId;
          _selectedAdvanceType = advance.advanceType;
          _amountController.text = advance.amount.toString();
          _monthlyDeductionController.text =
              advance.monthlyDeduction?.toString() ?? '';
          _reasonController.text = advance.reason ?? '';
          _notesController.text = advance.notes ?? '';
          _requestDate = advance.requestDate;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات السلفة: $e');
      Get.snackbar('خطأ', 'فشل في تحميل بيانات السلفة');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'تعديل السلفة' : 'طلب سلفة جديدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _submitForm,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: _isLoading ? Colors.grey : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات أساسية
                    _buildSectionCard(
                      title: 'المعلومات الأساسية',
                      icon: Icons.info_outline,
                      children: [
                        // اختيار الموظف
                        _buildEmployeeDropdown(),
                        const SizedBox(height: 16),

                        // نوع السلفة
                        _buildAdvanceTypeDropdown(),
                        const SizedBox(height: 16),

                        // تاريخ الطلب
                        _buildRequestDatePicker(),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // المعلومات المالية
                    _buildSectionCard(
                      title: 'المعلومات المالية',
                      icon: Icons.monetization_on,
                      children: [
                        // مبلغ السلفة
                        _buildAmountField(),
                        const SizedBox(height: 16),

                        // القسط الشهري (اختياري)
                        _buildMonthlyDeductionField(),

                        // معلومات إضافية عن القسط
                        if (_monthlyDeductionController.text.isNotEmpty)
                          _buildInstallmentInfo(),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // التفاصيل والملاحظات
                    _buildSectionCard(
                      title: 'التفاصيل والملاحظات',
                      icon: Icons.description,
                      children: [
                        // سبب السلفة
                        _buildReasonField(),
                        const SizedBox(height: 16),

                        // ملاحظات إضافية
                        _buildNotesField(),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // أزرار الإجراءات
                    _buildActionButtons(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء قائمة اختيار الموظف
  Widget _buildEmployeeDropdown() {
    return Obx(() {
      // إذا كانت قائمة الموظفين فارغة، عرض مؤشر التحميل
      if (employees.isEmpty) {
        return Container(
          height: 60,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 10),
                Text('جاري تحميل الموظفين...'),
              ],
            ),
          ),
        );
      }

      // التأكد من أن القيمة المختارة موجودة في القائمة
      final validEmployeeId =
          employees.any((emp) => emp['id'] == _selectedEmployeeId)
              ? _selectedEmployeeId
              : null;

      // إذا كانت القيمة غير صحيحة، إعادة تعيينها
      if (_selectedEmployeeId != null && validEmployeeId == null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedEmployeeId = null;
          });
        });
      }

      return DropdownButtonFormField<int>(
        value: validEmployeeId,
        decoration: InputDecoration(
          labelText: 'الموظف *',
          hintText: 'اختر الموظف',
          prefixIcon: const Icon(Icons.person),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        items: employees.map<DropdownMenuItem<int>>((employee) {
          return DropdownMenuItem<int>(
            value: employee['id'] as int,
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 350, maxHeight: 40),
              padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Text(
                  //   employee['name']?.toString() ?? 'غير محدد',
                  //   style: const TextStyle(
                  //     fontWeight: FontWeight.bold,
                  //     fontSize: 12,
                  //   ),
                  //   overflow: TextOverflow.ellipsis,
                  //   maxLines: 1,
                  // ),
                  const SizedBox(height: 1),
                  Row(
                    children: [
                      if (employee['employeeId'] != null &&
                          employee['employeeId'].toString().isNotEmpty)
                        Flexible(
                          child: Text(
                            'رقم: ${employee['employeeId']}',
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.blue.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      SizedBox(
                        width: 8,
                      ),
                      if (employee['employeeId'] != null &&
                          employee['employeeId'].toString().isNotEmpty)
                        const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          employee['name']?.toString() ?? 'غير محدد',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedEmployeeId = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'يجب اختيار الموظف';
          }
          return null;
        },
      );
    });
  }

  /// بناء قائمة اختيار نوع السلفة
  Widget _buildAdvanceTypeDropdown() {
    final advanceTypes = controller.getAdvanceTypes();

    return DropdownButtonFormField<String>(
      value: _selectedAdvanceType,
      decoration: InputDecoration(
        labelText: 'نوع السلفة *',
        hintText: 'اختر نوع السلفة',
        prefixIcon: const Icon(Icons.category),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      items: advanceTypes.map((type) {
        return DropdownMenuItem<String>(
          value: type['value'],
          child: Container(
            constraints: const BoxConstraints(maxHeight: 35),
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  type['label']!,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                // Text(
                //   type['description']!,
                //   style: TextStyle(
                //     fontSize: 11,
                //     color: Colors.grey.shade600,
                //   ),
                //   overflow: TextOverflow.ellipsis,
                //   maxLines: 1,
                // ),
              ],
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedAdvanceType = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يجب اختيار نوع السلفة';
        }
        return null;
      },
    );
  }

  /// بناء منتقي تاريخ الطلب
  Widget _buildRequestDatePicker() {
    return TextFormField(
      readOnly: true,
      decoration: InputDecoration(
        labelText: 'تاريخ الطلب *',
        hintText: 'اختر تاريخ الطلب',
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      controller: TextEditingController(
        text: '${_requestDate.day}/${_requestDate.month}/${_requestDate.year}',
      ),
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _requestDate,
          firstDate: DateTime.now().subtract(const Duration(days: 30)),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        if (date != null) {
          setState(() {
            _requestDate = date;
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'تاريخ الطلب مطلوب';
        }
        return null;
      },
    );
  }

  /// بناء حقل مبلغ السلفة
  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: InputDecoration(
        labelText: 'مبلغ السلفة *',
        hintText: 'أدخل مبلغ السلفة بالريال اليمني',
        prefixIcon: const Icon(Icons.attach_money),
        suffixText: 'ريال يمني',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'مبلغ السلفة مطلوب';
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return 'يجب أن يكون المبلغ أكبر من صفر';
        }
        if (amount > 5000000) {
          // 5 مليون ريال يمني
          return 'المبلغ كبير جداً (الحد الأقصى 5,000,000 ريال يمني)';
        }
        return null;
      },
      onChanged: (value) {
        setState(() {}); // لتحديث معلومات القسط
      },
    );
  }

  /// بناء حقل القسط الشهري
  Widget _buildMonthlyDeductionField() {
    return TextFormField(
      controller: _monthlyDeductionController,
      decoration: InputDecoration(
        labelText: 'القسط الشهري (اختياري)',
        hintText: 'أدخل مبلغ القسط الشهري',
        prefixIcon: const Icon(Icons.schedule),
        suffixText: 'ريال يمني',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
      ],
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final amount = double.tryParse(value);
          if (amount == null || amount <= 0) {
            return 'يجب أن يكون القسط أكبر من صفر';
          }
          final totalAmount = double.tryParse(_amountController.text);
          if (totalAmount != null && amount > totalAmount) {
            return 'القسط لا يمكن أن يكون أكبر من المبلغ الإجمالي';
          }
        }
        return null;
      },
      onChanged: (value) {
        setState(() {}); // لتحديث معلومات القسط
      },
    );
  }

  /// بناء معلومات القسط
  Widget _buildInstallmentInfo() {
    final totalAmount = double.tryParse(_amountController.text);
    final monthlyAmount = double.tryParse(_monthlyDeductionController.text);

    if (totalAmount == null || monthlyAmount == null || monthlyAmount <= 0) {
      return const SizedBox.shrink();
    }

    final months = (totalAmount / monthlyAmount).ceil();

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue.shade700, size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات القسط',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('عدد الأقساط المتوقع: $months شهر'),
          Text('إجمالي المبلغ: ${totalAmount.toStringAsFixed(0)} ريال يمني'),
          Text('القسط الشهري: ${monthlyAmount.toStringAsFixed(0)} ريال يمني'),
        ],
      ),
    );
  }

  /// بناء حقل سبب السلفة
  Widget _buildReasonField() {
    return TextFormField(
      controller: _reasonController,
      decoration: InputDecoration(
        labelText: 'سبب السلفة',
        hintText: 'اذكر سبب طلب السلفة',
        prefixIcon: const Icon(Icons.help_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      maxLines: 3,
      validator: (value) {
        if (value != null && value.length > 500) {
          return 'السبب طويل جداً';
        }
        return null;
      },
    );
  }

  /// بناء حقل الملاحظات
  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: InputDecoration(
        labelText: 'ملاحظات إضافية',
        hintText: 'أي ملاحظات أو تفاصيل إضافية',
        prefixIcon: const Icon(Icons.note),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      maxLines: 3,
      validator: (value) {
        if (value != null && value.length > 1000) {
          return 'الملاحظات طويلة جداً';
        }
        return null;
      },
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(_isEditMode ? 'حفظ التعديلات' : 'حفظ الطلب'),
          ),
        ),
      ],
    );
  }

  /// إرسال النموذج
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final advanceData = CreateAdvanceDto(
        employeeId: _selectedEmployeeId!,
        advanceType: _selectedAdvanceType!,
        amount: double.parse(_amountController.text),
        monthlyDeduction: _monthlyDeductionController.text.isNotEmpty
            ? double.parse(_monthlyDeductionController.text)
            : null,
        requestDate: _requestDate,
        reason:
            _reasonController.text.isNotEmpty ? _reasonController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      bool success;
      if (_isEditMode && _editingAdvanceId != null) {
        // TODO: إضافة دالة updateAdvance في Controller
        success = false; // مؤقتاً
        Get.snackbar(
          'معلومات',
          'ميزة التعديل قيد التطوير',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          icon: const Icon(Icons.info, color: Colors.white),
        );
      } else {
        debugPrint('🔄 بدء إنشاء السلفة...');
        success = await controller.createAdvance(advanceData);
        debugPrint('✅ نتيجة إنشاء السلفة: $success');
      }

      debugPrint('📋 معالجة نتيجة العملية: success = $success');

      if (success) {
        debugPrint('✅ نجحت العملية - إظهار رسالة النجاح');
        // إظهار رسالة نجاح مع خيار العودة الفورية
        Get.snackbar(
          '✅ نجح',
          _isEditMode ? 'تم تحديث السلفة بنجاح' : 'تم إضافة السلفة بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          icon: const Icon(Icons.check_circle, color: Colors.white),
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.TOP,
          margin: const EdgeInsets.all(10),
          mainButton: TextButton(
            onPressed: () {
              Get.closeCurrentSnackbar();
            Get.back();
            },
            child: const Text(
              'العودة الآن',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        );

        // انتظار قصير ثم العودة التلقائية
        await Future.delayed(const Duration(milliseconds: 800));
        debugPrint('🔙 العودة للشاشة السابقة');

        // محاولة العودة بطرق متعددة لضمان النجاح
        try {
          // التحقق من أن الـ context لا يزال صالحاً
          if (mounted) {
            if (Navigator.of(context).canPop()) {
              Get.back(); // العودة للشاشة السابقة
              debugPrint('✅ تم استخدام Get.back()');
            } else {
              // إذا لم يكن بإمكان العودة، الانتقال لشاشة السلف الرئيسية
              Get.offNamed('/hr/advances');
              debugPrint('✅ تم الانتقال لشاشة السلف الرئيسية');
            }
          } else {
            // إذا كان الـ widget غير mounted، استخدم Get فقط
            Get.back();
            debugPrint('✅ تم استخدام Get.back() (widget not mounted)');
          }
        } catch (e) {
          debugPrint('❌ خطأ في العودة: $e');
          // كحل أخير، الانتقال لشاشة السلف الرئيسية
          Get.offNamed('/hr/advances');
          debugPrint('✅ تم الانتقال لشاشة السلف الرئيسية كحل أخير');
        }
      } else {
        debugPrint('❌ فشلت العملية - إظهار رسالة الفشل');
        // إظهار رسالة فشل
        Get.snackbar(
          '❌ فشل',
          _isEditMode ? 'فشل في تحديث السلفة' : 'فشل في إضافة السلفة',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          icon: const Icon(Icons.error, color: Colors.white),
          duration: const Duration(seconds: 4),
          snackPosition: SnackPosition.TOP,
          margin: const EdgeInsets.all(10),
        );
      }
    } catch (e) {
      debugPrint('خطأ في حفظ السلفة: $e');
      Get.snackbar(
        'خطأ',
        _isEditMode
            ? 'حدث خطأ أثناء تحديث السلفة. يرجى المحاولة مرة أخرى.'
            : 'حدث خطأ أثناء إضافة السلفة. يرجى المحاولة مرة أخرى.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
        duration: const Duration(seconds: 4),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
