{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["fVpQxzntCFyB6OS1S946i8zgCVEsVFCivaq0VmdDyEM=", "u6u+4i4Z1CHMymiA6KjFo7M5S33t+LVADvmEivBslWU=", "IK9Sdgm3OmGZoRh1sNV1ghYFp1/v8PuwM2fWtX2rPFw=", "MvTsLOGhTkRD60UC6Ore/jPhzuHqNkSy1aqnsIeYicA=", "Dkfzwt0Kom8wMXWQDlSSIWucZdhes2aCIomPkf/Tjr4=", "IEXDXHsUMaOV+o99QonrEggY8drEHKjsWqShKKL7L4g="], "CachedAssets": {"MvTsLOGhTkRD60UC6Ore/jPhzuHqNkSy1aqnsIeYicA=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o41damrri7", "Integrity": "+BhJIc18LbkR9xPubzYGoOTvscpAep0aJdIKDE3C4iY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 760, "LastWriteTime": "2025-08-07T22:23:54.517976+00:00"}, "Dkfzwt0Kom8wMXWQDlSSIWucZdhes2aCIomPkf/Tjr4=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint=je3vbe9eb6}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "10h95t94gu", "Integrity": "r/irDppePcS310d2LVmwx2HBTOe0mT5mkQ3b6qzOv/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 5292, "LastWriteTime": "2025-08-07T22:23:54.517976+00:00"}, "IEXDXHsUMaOV+o99QonrEggY8drEHKjsWqShKKL7L4g=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5s4zpexv7b", "Integrity": "9YMMi2EC2DEXx7EStdzg86PLt4oYt6yUZkyWFt34sdE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7502, "LastWriteTime": "2025-08-07T22:23:54.5189754+00:00"}, "IK9Sdgm3OmGZoRh1sNV1ghYFp1/v8PuwM2fWtX2rPFw=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o41damrri7", "Integrity": "+BhJIc18LbkR9xPubzYGoOTvscpAep0aJdIKDE3C4iY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 760, "LastWriteTime": "2025-08-07T22:23:54.517976+00:00"}, "u6u+4i4Z1CHMymiA6KjFo7M5S33t+LVADvmEivBslWU=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw2a96sh2z", "Integrity": "1lEsoPMX7Ob4LE2JTx4RWp6Zr9ly50MfE4kbzSuzjsk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1944, "LastWriteTime": "2025-08-07T22:23:54.517976+00:00"}, "fVpQxzntCFyB6OS1S946i8zgCVEsVFCivaq0VmdDyEM=": {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aw2a96sh2z", "Integrity": "1lEsoPMX7Ob4LE2JTx4RWp6Zr9ly50MfE4kbzSuzjsk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1944, "LastWriteTime": "2025-08-07T22:23:54.517976+00:00"}}, "CachedCopyCandidates": {}}