import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../controllers/advance_controller.dart';
import '../models/advance_model.dart';
import '../routes/hr_routes.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// ويدجت لوحة تحكم السلف في الصفحة الرئيسية لـ HR
class AdvanceDashboardWidget extends StatelessWidget {
  const AdvanceDashboardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final AdvanceController controller = Get.put(AdvanceController());

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان والإجراءات
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'السلف والمساعدات المالية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Get.toNamed(HRRoutes.advances),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // الإحصائيات السريعة
            Obx(() => _buildQuickStats(controller)),
            
            const SizedBox(height: 16),
            
            // المخطط البياني
            Obx(() => _buildChart(controller)),
            
            const SizedBox(height: 16),
            
            // السلف الحديثة
            Obx(() => _buildRecentAdvances(controller)),
            
            const SizedBox(height: 16),
            
            // أزرار الإجراءات السريعة
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(AdvanceController controller) {
    final stats = controller.statistics.value;
    if (stats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي السلف',
            stats.totalAdvances.toString(),
            Icons.account_balance_wallet,
            AppColors.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'في الانتظار',
            stats.pendingAdvances.toString(),
            Icons.pending,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'موافق عليها',
            stats.approvedAdvances.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'المبلغ الإجمالي',
            '${(stats.totalAmount / 1000).toStringAsFixed(0)}ك ريال يمني',
            Icons.monetization_on,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء المخطط البياني
  Widget _buildChart(AdvanceController controller) {
    final stats = controller.statistics.value;
    if (stats == null) return const SizedBox.shrink();

    final chartData = [
      ChartData('في الانتظار', stats.pendingAdvances.toDouble(), Colors.orange),
      ChartData('موافق عليها', stats.approvedAdvances.toDouble(), Colors.green),
      ChartData('مرفوضة', stats.rejectedAdvances.toDouble(), Colors.red),
      ChartData('مكتملة', stats.completedAdvances.toDouble(), Colors.blue),
    ];

    return SizedBox(
      height: 200,
      child: SfCircularChart(
        title: ChartTitle(text: 'توزيع السلف حسب الحالة'),
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          textStyle: const TextStyle(fontSize: 10),
        ),
        series: <PieSeries<ChartData, String>>[
          PieSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.category,
            yValueMapper: (ChartData data, _) => data.value,
            pointColorMapper: (ChartData data, _) => data.color,
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.outside,
              textStyle: TextStyle(fontSize: 10),
            ),
            enableTooltip: true,
          ),
        ],
      ),
    );
  }

  /// بناء السلف الحديثة
  Widget _buildRecentAdvances(AdvanceController controller) {
    final advances = controller.advances.take(3).toList();
    
    if (advances.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text('لا توجد سلف حديثة'),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'السلف الحديثة',
          style: AppStyles.titleSmall.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        ...advances.map((advance) => _buildAdvanceItem(advance)),
      ],
    );
  }

  /// بناء عنصر سلفة
  Widget _buildAdvanceItem(EmployeeAdvance advance) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // أيقونة نوع السلفة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getAdvanceTypeColor(advance.advanceType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              _getAdvanceTypeIcon(advance.advanceType),
              color: _getAdvanceTypeColor(advance.advanceType),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          
          // معلومات السلفة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  advance.employeeNameArabic ?? advance.employeeName ?? 'غير محدد',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  advance.advanceTypeArabic,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // المبلغ والحالة
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${advance.amount.toStringAsFixed(0)} ريال يمني',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              _buildStatusChip(advance.status, advance.statusArabic),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء شارة الحالة
  Widget _buildStatusChip(String status, String statusArabic) {
    Color color;
    switch (status) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'approved':
        color = Colors.green;
        break;
      case 'rejected':
        color = Colors.red;
        break;
      case 'completed':
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusArabic,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات السريعة
  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => Get.toNamed(HRRoutes.addAdvance),
            icon: const Icon(Icons.add, size: 18),
            label: const Text('طلب سلفة جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Get.toNamed(HRRoutes.advances),
            icon: const Icon(Icons.list, size: 18),
            label: const Text('إدارة السلف'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// الحصول على لون نوع السلفة
  Color _getAdvanceTypeColor(String advanceType) {
    switch (advanceType) {
      case 'salary_advance':
        return Colors.blue;
      case 'financial_assistance':
        return Colors.green;
      case 'loan':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع السلفة
  IconData _getAdvanceTypeIcon(String advanceType) {
    switch (advanceType) {
      case 'salary_advance':
        return Icons.account_balance_wallet;
      case 'financial_assistance':
        return Icons.help_outline;
      case 'loan':
        return Icons.monetization_on;
      default:
        return Icons.attach_money;
    }
  }
}

/// نموذج بيانات المخطط
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}
