import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:collection/collection.dart';

import '../../controllers/advance_controller.dart';
import '../../models/advance_model.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';
import '../../../../widgets/error_message.dart';
import '../../../../helpers/user_helper.dart';

/// شاشة تفاصيل السلفة
class AdvanceDetailsScreen extends StatefulWidget {
  const AdvanceDetailsScreen({super.key});

  @override
  State<AdvanceDetailsScreen> createState() => _AdvanceDetailsScreenState();
}

class _AdvanceDetailsScreenState extends State<AdvanceDetailsScreen> {
  final AdvanceController controller = Get.find<AdvanceController>();
  late final int advanceId;

  @override
  void initState() {
    super.initState();

    // استخراج معرف السلفة من URL parameters أو arguments
    try {
      // محاولة الحصول على المعرف من URL parameters أولاً
      final String? idParam = Get.parameters['id'];
      if (idParam != null) {
        advanceId = int.parse(idParam);
        debugPrint('✅ تم استخراج معرف السلفة من URL: $advanceId');
      } else {
        // إذا لم يوجد في URL، محاولة الحصول عليه من arguments
        final dynamic args = Get.arguments;
        if (args is int) {
          advanceId = args;
          debugPrint('✅ تم استخراج معرف السلفة من arguments: $advanceId');
        } else if (args is Map && args.containsKey('id')) {
          advanceId = args['id'] as int;
          debugPrint('✅ تم استخراج معرف السلفة من arguments map: $advanceId');
        } else {
          throw Exception('لم يتم العثور على معرف السلفة');
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في استخراج معرف السلفة: $e');
      // العودة للشاشة السابقة في حالة الخطأ
      Get.back();
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على معرف السلفة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // تحميل تفاصيل السلفة
    _loadAdvanceDetails();
  }



  /// تحميل تفاصيل السلفة
  Future<void> _loadAdvanceDetails() async {
    try {
      // إذا لم تكن السلفة محددة أو مختلفة، قم بتحميلها
      if (controller.selectedAdvance.value?.id != advanceId) {
        debugPrint('🔄 تحميل تفاصيل السلفة رقم: $advanceId');

        // البحث عن السلفة في القائمة الحالية أولاً
        final existingAdvance = controller.advances.firstWhereOrNull(
          (advance) => advance.id == advanceId,
        );

        if (existingAdvance != null) {
          // إذا وُجدت في القائمة، استخدمها
          controller.selectAdvance(existingAdvance);
          debugPrint('✅ تم العثور على السلفة في القائمة المحلية');
        } else {
          // إذا لم توجد، حاول تحميلها من API
          debugPrint('🌐 تحميل السلفة من API...');
          // TODO: إضافة دالة loadAdvanceById في Controller
          // await controller.loadAdvanceById(advanceId);

          // مؤقتاً: إظهار رسالة خطأ
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على السلفة المطلوبة',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          Get.back();
        }
      } else {
        debugPrint('✅ السلفة محملة مسبقاً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل تفاصيل السلفة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ في تحميل تفاصيل السلفة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل السلفة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('تعديل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'approve',
                child: ListTile(
                  leading: Icon(Icons.check_circle),
                  title: Text('موافقة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'reject',
                child: ListTile(
                  leading: Icon(Icons.cancel),
                  title: Text('رفض'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'payment',
                child: ListTile(
                  leading: Icon(Icons.payment),
                  title: Text('تسجيل دفعة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete),
                  title: Text('حذف'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        final advance = controller.selectedAdvance.value;
        
        if (controller.isLoading.value) {
          return const LoadingIndicator();
        }

        if (controller.error.value.isNotEmpty) {
          return ErrorMessage(
            message: controller.error.value,
            onRetry: _loadAdvanceDetails,
          );
        }

        if (advance == null) {
          return const Center(
            child: Text('لم يتم العثور على السلفة'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildBasicInfoCard(advance),
              const SizedBox(height: 16),
              
              // معلومات مالية
              _buildFinancialInfoCard(advance),
              const SizedBox(height: 16),
              
              // معلومات الموافقة
              if (advance.status != 'pending')
                _buildApprovalInfoCard(advance),
              
              const SizedBox(height: 16),
              
              // تفاصيل إضافية
              _buildAdditionalDetailsCard(advance),
              
              const SizedBox(height: 16),
              
              // أزرار الإجراءات
              _buildActionButtons(advance),
            ],
          ),
        );
      }),
    );
  }

  /// بناء بطاقة المعلومات الأساسية
  Widget _buildBasicInfoCard(EmployeeAdvance advance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'المعلومات الأساسية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                _buildStatusChip(advance.status, advance.statusArabic),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('رقم السلفة', '#${advance.id}'),
            _buildInfoRow('اسم الموظف', advance.employeeNameArabic ?? advance.employeeName ?? 'غير محدد'),
            _buildInfoRow('نوع السلفة', advance.advanceTypeArabic),
            _buildInfoRow('تاريخ الطلب', _formatDate(advance.requestDate)),
            if (advance.approvalDate != null)
              _buildInfoRow('تاريخ الموافقة', _formatDate(advance.approvalDate!)),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات المالية
  Widget _buildFinancialInfoCard(EmployeeAdvance advance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monetization_on, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'المعلومات المالية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // شريط التقدم
            _buildProgressBar(advance),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildAmountCard(
                    'المبلغ الإجمالي',
                    '${advance.amount.toStringAsFixed(0)} ريال يمني',
                    Colors.blue,
                    Icons.account_balance_wallet,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildAmountCard(
                    'المبلغ المدفوع',
                    '${advance.paidAmount.toStringAsFixed(0)} ريال يمني',
                    Colors.green,
                    Icons.check_circle,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildAmountCard(
                    'المبلغ المتبقي',
                    '${advance.remainingAmount.toStringAsFixed(0)} ريال يمني',
                    Colors.orange,
                    Icons.pending,
                  ),
                ),
                const SizedBox(width: 12),
                if (advance.monthlyDeduction != null)
                  Expanded(
                    child: _buildAmountCard(
                      'القسط الشهري',
                      '${advance.monthlyDeduction!.toStringAsFixed(0)} ريال يمني',
                      Colors.purple,
                      Icons.schedule,
                    ),
                  ),
              ],
            ),
            
            if (advance.estimatedMonthsToComplete != null)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.schedule, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'المدة المتوقعة للسداد: ${advance.estimatedMonthsToComplete} شهر',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar(EmployeeAdvance advance) {
    final progress = advance.completionPercentage / 100;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('نسبة السداد'),
            Text(
              '${advance.completionPercentage.toStringAsFixed(1)}%',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            advance.isCompleted ? Colors.green : AppColors.primary,
          ),
          minHeight: 8,
        ),
      ],
    );
  }

  /// بناء بطاقة مبلغ
  Widget _buildAmountCard(String title, String amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            amount,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات الموافقة
  Widget _buildApprovalInfoCard(EmployeeAdvance advance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  advance.status == 'approved' ? Icons.check_circle : Icons.cancel,
                  color: advance.status == 'approved' ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات الموافقة',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: advance.status == 'approved' ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (advance.approvalDate != null)
              _buildInfoRow('تاريخ الموافقة/الرفض', _formatDate(advance.approvalDate!)),
            if (advance.approverName != null)
              _buildInfoRow('المعتمد من قبل', advance.approverName!),
            if (advance.startDeductionDate != null)
              _buildInfoRow('تاريخ بداية الخصم', _formatDate(advance.startDeductionDate!)),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التفاصيل الإضافية
  Widget _buildAdditionalDetailsCard(EmployeeAdvance advance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'التفاصيل الإضافية',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (advance.reason != null && advance.reason!.isNotEmpty)
              _buildInfoSection('سبب السلفة', advance.reason!),

            if (advance.notes != null && advance.notes!.isNotEmpty)
              _buildInfoSection('ملاحظات', advance.notes!),

            _buildInfoRow('تاريخ الإنشاء', _formatTimestamp(advance.createdAt)),
            if (advance.updatedAt != null)
              _buildInfoRow('آخر تحديث', _formatTimestamp(advance.updatedAt!)),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(EmployeeAdvance advance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'الإجراءات المتاحة',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // تعديل (فقط للسلف في حالة pending)
                if (advance.status == 'pending')
                  _buildActionButton(
                    'تعديل',
                    Icons.edit,
                    Colors.blue,
                    () => _editAdvance(advance),
                  ),

                // موافقة (فقط للسلف في حالة pending)
                if (advance.status == 'pending')
                  _buildActionButton(
                    'موافقة',
                    Icons.check_circle,
                    Colors.green,
                    () => _approveAdvance(advance),
                  ),

                // رفض (فقط للسلف في حالة pending)
                if (advance.status == 'pending')
                  _buildActionButton(
                    'رفض',
                    Icons.cancel,
                    Colors.red,
                    () => _rejectAdvance(advance),
                  ),

                // تسجيل دفعة (للسلف المعتمدة وغير المكتملة)
                if (advance.status == 'approved' && !advance.isCompleted)
                  _buildActionButton(
                    'تسجيل دفعة',
                    Icons.payment,
                    Colors.purple,
                    () => _recordPayment(advance),
                  ),

                // حذف (فقط للسلف في حالة pending)
                if (advance.status == 'pending')
                  _buildActionButton(
                    'حذف',
                    Icons.delete,
                    Colors.red,
                    () => _deleteAdvance(advance),
                  ),

                // طباعة
                _buildActionButton(
                  'طباعة',
                  Icons.print,
                  Colors.grey,
                  () => _printAdvance(advance),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم معلومات
  Widget _buildInfoSection(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شارة الحالة
  Widget _buildStatusChip(String status, String statusArabic) {
    Color color;
    switch (status) {
      case 'pending':
        color = Colors.orange;
        break;
      case 'approved':
        color = Colors.green;
        break;
      case 'rejected':
        color = Colors.red;
        break;
      case 'completed':
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        statusArabic,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنسيق الطابع الزمني
  String _formatTimestamp(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    final advance = controller.selectedAdvance.value;
    if (advance == null) return;

    switch (action) {
      case 'edit':
        _editAdvance(advance);
        break;
      case 'approve':
        _approveAdvance(advance);
        break;
      case 'reject':
        _rejectAdvance(advance);
        break;
      case 'payment':
        _recordPayment(advance);
        break;
      case 'delete':
        _deleteAdvance(advance);
        break;
    }
  }

  /// تعديل السلفة
  void _editAdvance(EmployeeAdvance advance) {
    // TODO: الانتقال لشاشة التعديل
    Get.snackbar('معلومات', 'سيتم تنفيذ هذه الميزة قريباً');
  }

  /// الموافقة على السلفة
  void _approveAdvance(EmployeeAdvance advance) {
    _showApprovalDialog(advance, true);
  }

  /// رفض السلفة
  void _rejectAdvance(EmployeeAdvance advance) {
    _showApprovalDialog(advance, false);
  }

  /// عرض حوار الموافقة/الرفض
  void _showApprovalDialog(EmployeeAdvance advance, bool isApproval) {
    final reasonController = TextEditingController();
    final notesController = TextEditingController();
    DateTime? startDeductionDate;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isApproval ? 'موافقة على السلفة' : 'رفض السلفة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isApproval) ...[
                  // تاريخ بداية الخصم
                  ListTile(
                    title: const Text('تاريخ بداية الخصم'),
                    subtitle: Text(startDeductionDate != null
                        ? _formatDate(startDeductionDate!)
                        : 'اختر التاريخ'),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() {
                          startDeductionDate = date;
                        });
                      }
                    },
                  ),
                ] else ...[
                  // سبب الرفض
                  TextField(
                    controller: reasonController,
                    decoration: const InputDecoration(
                      labelText: 'سبب الرفض *',
                      hintText: 'اذكر سبب رفض السلفة',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                ],

                // ملاحظات إضافية
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات إضافية',
                    hintText: 'أي ملاحظات أخرى',
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (!isApproval && reasonController.text.trim().isEmpty) {
                  Get.snackbar('خطأ', 'سبب الرفض مطلوب');
                  return;
                }

                // الحصول على معرف المستخدم الحالي
                final currentUser = UserHelper.getCurrentUser();
                if (currentUser == null) {
                  Get.snackbar('خطأ', 'لم يتم العثور على بيانات المستخدم الحالي');
                  return;
                }

                final approvalData = ApproveAdvanceDto(
                  isApproved: isApproval,
                  approvedBy: currentUser.id,
                  startDeductionDate: startDeductionDate,
                  rejectionReason: isApproval ? null : reasonController.text.trim(),
                  notes: notesController.text.trim().isNotEmpty
                      ? notesController.text.trim()
                      : null,
                );

                Navigator.of(context).pop();

                final success = await controller.approveAdvance(advance.id, approvalData);
                if (success) {
                  await controller.refreshSelectedAdvance();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isApproval ? Colors.green : Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(isApproval ? 'موافقة' : 'رفض'),
            ),
          ],
        ),
      ),
    );
  }

  /// تسجيل دفعة
  void _recordPayment(EmployeeAdvance advance) {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    DateTime paymentDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تسجيل دفعة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // مبلغ الدفعة
                TextField(
                  controller: amountController,
                  decoration: InputDecoration(
                    labelText: 'مبلغ الدفعة *',
                    hintText: 'أدخل مبلغ الدفعة',
                    suffixText: 'ريال يمني',
                    helperText: 'المتبقي: ${advance.remainingAmount.toStringAsFixed(0)} ريال يمني',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),

                // تاريخ الدفعة
                ListTile(
                  title: const Text('تاريخ الدفعة'),
                  subtitle: Text(_formatDate(paymentDate)),
                  trailing: const Icon(Icons.calendar_today),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: paymentDate,
                      firstDate: DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        paymentDate = date;
                      });
                    }
                  },
                ),

                // ملاحظات
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    hintText: 'أي ملاحظات حول الدفعة',
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amount = double.tryParse(amountController.text);
                if (amount == null || amount <= 0) {
                  Get.snackbar('خطأ', 'مبلغ الدفعة غير صحيح');
                  return;
                }
                if (amount > advance.remainingAmount) {
                  Get.snackbar('خطأ', 'مبلغ الدفعة أكبر من المتبقي');
                  return;
                }

                final paymentData = AdvancePaymentDto(
                  paymentAmount: amount,
                  paymentDate: paymentDate,
                  notes: notesController.text.trim().isNotEmpty
                      ? notesController.text.trim()
                      : null,
                );

                Navigator.of(context).pop();

                final success = await controller.recordPayment(advance.id, paymentData);
                if (success) {
                  await controller.refreshSelectedAdvance();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('تسجيل'),
            ),
          ],
        ),
      ),
    );
  }

  /// حذف السلفة
  void _deleteAdvance(EmployeeAdvance advance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه السلفة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final success = await controller.deleteAdvance(advance.id);
              if (success) {
                Get.back(); // العودة للشاشة السابقة
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// طباعة السلفة
  void _printAdvance(EmployeeAdvance advance) {
    // TODO: تنفيذ الطباعة
    Get.snackbar('معلومات', 'سيتم تنفيذ الطباعة قريباً');
  }
}
