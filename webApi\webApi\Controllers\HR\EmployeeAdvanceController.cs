using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.HR;
using webApi.Services;
using System.ComponentModel.DataAnnotations;

namespace webApi.Controllers.HR
{
    /// <summary>
    /// Controller لإدارة السلف والمساعدات المالية للموظفين
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class EmployeeAdvanceController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<EmployeeAdvanceController> _logger;
        private readonly ILoggingService _loggingService;

        public EmployeeAdvanceController(
            TasksDbContext context, 
            ILogger<EmployeeAdvanceController> logger, 
            ILoggingService loggingService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// الحصول على جميع السلف مع الفلترة والترقيم
        /// </summary>
        /// <param name="page">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="status">حالة السلفة</param>
        /// <param name="advanceType">نوع السلفة</param>
        /// <param name="departmentId">معرف القسم</param>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة السلف مع معلومات الترقيم</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAllAdvances(
            int page = 1,
            int pageSize = 20,
            string? status = null,
            string? advanceType = null,
            int? departmentId = null,
            string? searchTerm = null)
        {
            try
            {
                var query = _context.EmployeeAdvances
                    .Include(a => a.Employee)
                    .Include(a => a.Approver)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(a => a.Status == status);
                }

                if (!string.IsNullOrEmpty(advanceType))
                {
                    query = query.Where(a => a.AdvanceType == advanceType);
                }

                if (departmentId.HasValue)
                {
                    query = query.Where(a => a.Employee.DepartmentId == departmentId.Value);
                }

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(a =>
                        a.Employee.Username.Contains(searchTerm) ||
                        a.Employee.FullNameArabic.Contains(searchTerm) ||
                        a.Reason.Contains(searchTerm) ||
                        a.Notes.Contains(searchTerm));
                }

                // حساب العدد الإجمالي
                var totalRecords = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // تطبيق الترقيم
                var advances = await query
                    .OrderByDescending(a => a.RequestDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(a => new
                    {
                        a.Id,
                        a.EmployeeId,
                        a.AdvanceType,
                        a.Amount,
                        a.RemainingAmount,
                        a.MonthlyDeduction,
                        a.RequestDate,
                        a.ApprovalDate,
                        a.StartDeductionDate,
                        a.Status,
                        a.Reason,
                        a.ApprovedBy,
                        a.Notes,
                        a.CreatedAt,
                        a.UpdatedAt,
                        EmployeeName = a.Employee.Username,
                        EmployeeNameArabic = a.Employee.FullNameArabic,
                        ApproverName = a.Approver != null ? a.Approver.Username : null
                    })
                    .ToListAsync();

                await _loggingService.LogActivityAsync(
                    "GetAllAdvances",
                    "EmployeeAdvance",
                    0,
                    0,
                    $"تم تحميل السلف - الصفحة {page} من {totalPages}"
                );

                return Ok(new
                {
                    advances,
                    totalRecords,
                    totalPages,
                    currentPage = page,
                    pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل السلف");
                // await _loggingService.LogSystemAsync("Error", "EmployeeAdvance", ex.Message, null, Request.HttpContext.Connection.RemoteIpAddress?.ToString());
                return StatusCode(500, new { message = "حدث خطأ في تحميل السلف", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على السلف والمساعدات لموظف محدد
        /// </summary>
        /// <param name="employeeId">معرف الموظف</param>
        /// <param name="status">حالة السلفة (اختياري)</param>
        /// <param name="advanceType">نوع السلفة (اختياري)</param>
        /// <returns>قائمة السلف والمساعدات</returns>
        [HttpGet("employee/{employeeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<object>>> GetEmployeeAdvances(
            int employeeId,
            string? status = null,
            string? advanceType = null)
        {
            try
            {
                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(employeeId);
                if (employee == null || employee.IsDeleted)
                {
                    return NotFound(new { message = "الموظف غير موجود" });
                }

                var query = _context.EmployeeAdvances
                    .Include(a => a.Employee)
                    .Include(a => a.Approver)
                    .Where(a => a.EmployeeId == employeeId);

                // تطبيق فلتر الحالة
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(a => a.Status == status);
                }

                var advances = await query
                    .OrderByDescending(a => a.RequestDate)
                    .ToListAsync();

                return Ok(advances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على السلف للموظف {EmployeeId}", employeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على سلفة محددة
        /// </summary>
        /// <param name="id">معرف السلفة</param>
        /// <returns>بيانات السلفة</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EmployeeAdvance>> GetAdvance(int id)
        {
            try
            {
                var advance = await _context.EmployeeAdvances
                    .Include(a => a.Employee)
                    .Include(a => a.Approver)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (advance == null)
                {
                    return NotFound(new { message = "السلفة غير موجودة" });
                }

                return Ok(advance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على السلفة {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// طلب سلفة جديدة
        /// </summary>
        /// <param name="dto">بيانات طلب السلفة</param>
        /// <returns>السلفة المُنشأة</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EmployeeAdvance>> CreateAdvance([FromBody] CreateAdvanceDto dto)
        {
            try
            {
                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(dto.EmployeeId);
                if (employee == null || employee.IsDeleted)
                {
                    return NotFound(new { message = "الموظف غير موجود" });
                }

                // التحقق من عدم وجود سلف معلقة للموظف
                var pendingAdvances = await _context.EmployeeAdvances
                    .Where(a => a.EmployeeId == dto.EmployeeId && 
                               (a.Status == "pending" || a.Status == "approved") &&
                               a.RemainingAmount > 0)
                    .CountAsync();

                if (pendingAdvances >= 3) // حد أقصى 3 سلف نشطة
                {
                    return BadRequest(new { message = "لا يمكن طلب سلفة جديدة، يوجد 3 سلف نشطة بالفعل" });
                }

                var advance = new EmployeeAdvance
                {
                    EmployeeId = dto.EmployeeId,
                    AdvanceType = dto.AdvanceType,
                    Amount = dto.Amount,
                    RemainingAmount = dto.Amount, // في البداية المبلغ المتبقي = المبلغ الكامل
                    MonthlyDeduction = dto.MonthlyDeduction,
                    RequestDate = dto.RequestDate,
                    Status = "pending",
                    Reason = dto.Reason,
                    Notes = dto.Notes,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.EmployeeAdvances.Add(advance);
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "طلب سلفة جديدة",
                    "EmployeeAdvance",
                    advance.Id,
                    dto.EmployeeId,
                    $"تم طلب سلفة من نوع {dto.AdvanceType} بمبلغ {dto.Amount:C}"
                );

                return CreatedAtAction(nameof(GetAdvance), new { id = advance.Id }, advance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في إنشاء طلب السلفة");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الموافقة على سلفة أو رفضها
        /// </summary>
        /// <param name="id">معرف السلفة</param>
        /// <param name="dto">بيانات الموافقة أو الرفض</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPost("{id}/approve")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> ApproveAdvance(int id, [FromBody] ApproveAdvanceDto dto)
        {
            try
            {
                var advance = await _context.EmployeeAdvances.FindAsync(id);
                if (advance == null)
                {
                    return NotFound(new { message = "السلفة غير موجودة" });
                }

                if (advance.Status != "pending")
                {
                    return BadRequest(new { message = "لا يمكن تغيير حالة السلفة، الحالة الحالية: " + advance.Status });
                }

                // التحقق من وجود المعتمد
                var approver = await _context.Users.FindAsync(dto.ApprovedBy);
                if (approver == null || approver.IsDeleted)
                {
                    return NotFound(new { message = "المعتمد غير موجود" });
                }

                advance.Status = dto.IsApproved ? "approved" : "rejected";
                advance.ApprovedBy = dto.ApprovedBy;
                advance.ApprovalDate = DateTime.Now;
                advance.StartDeductionDate = dto.IsApproved ? dto.StartDeductionDate : null;
                advance.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                if (!dto.IsApproved && !string.IsNullOrEmpty(dto.RejectionReason))
                {
                    advance.Notes = (advance.Notes ?? "") + "\nسبب الرفض: " + dto.RejectionReason;
                }

                await _context.SaveChangesAsync();

                // تسجيل العملية
                var action = dto.IsApproved ? "الموافقة على سلفة" : "رفض سلفة";
                var details = dto.IsApproved 
                    ? $"تم الموافقة على السلفة بمبلغ {advance.Amount:C}"
                    : $"تم رفض السلفة بمبلغ {advance.Amount:C}. السبب: {dto.RejectionReason}";

                await _loggingService.LogActivityAsync(action, "EmployeeAdvance", id, advance.EmployeeId, details);

                return Ok(new { message = dto.IsApproved ? "تم الموافقة على السلفة بنجاح" : "تم رفض السلفة" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الموافقة على السلفة {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تسجيل دفعة من السلفة
        /// </summary>
        /// <param name="id">معرف السلفة</param>
        /// <param name="dto">بيانات الدفعة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPost("{id}/payment")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> RecordPayment(int id, [FromBody] RecordPaymentDto dto)
        {
            try
            {
                var advance = await _context.EmployeeAdvances.FindAsync(id);
                if (advance == null)
                {
                    return NotFound(new { message = "السلفة غير موجودة" });
                }

                if (advance.Status != "approved")
                {
                    return BadRequest(new { message = "السلفة غير معتمدة" });
                }

                if (dto.PaymentAmount <= 0)
                {
                    return BadRequest(new { message = "مبلغ الدفعة يجب أن يكون أكبر من صفر" });
                }

                if (dto.PaymentAmount > advance.RemainingAmount)
                {
                    return BadRequest(new { message = "مبلغ الدفعة أكبر من المبلغ المتبقي" });
                }

                // تحديث المبلغ المتبقي
                advance.RemainingAmount -= dto.PaymentAmount;
                advance.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إذا تم سداد السلفة بالكامل، تغيير الحالة إلى مكتملة
                if (advance.RemainingAmount <= 0)
                {
                    advance.Status = "completed";
                }

                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "دفع من السلفة",
                    "EmployeeAdvance",
                    id,
                    advance.EmployeeId,
                    $"تم دفع {dto.PaymentAmount:C} من السلفة. المبلغ المتبقي: {advance.RemainingAmount:C}"
                );

                return Ok(new 
                { 
                    message = "تم تسجيل الدفعة بنجاح",
                    remainingAmount = advance.RemainingAmount,
                    isCompleted = advance.Status == "completed"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في تسجيل دفعة السلفة {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات السلف
        /// </summary>
        /// <returns>إحصائيات السلف</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAdvanceStatistics()
        {
            try
            {
                var advances = await _context.EmployeeAdvances.ToListAsync();

                var statistics = new
                {
                    TotalAdvances = advances.Count,
                    PendingAdvances = advances.Count(a => a.Status == "pending"),
                    ApprovedAdvances = advances.Count(a => a.Status == "approved"),
                    CompletedAdvances = advances.Count(a => a.Status == "completed"),
                    RejectedAdvances = advances.Count(a => a.Status == "rejected"),
                    TotalAmount = advances.Sum(a => a.Amount),
                    TotalRemainingAmount = advances.Where(a => a.Status == "approved").Sum(a => a.RemainingAmount),
                    AdvancesByType = advances.GroupBy(a => a.AdvanceType).ToDictionary(g => g.Key, g => g.Count()),
                    AverageAdvanceAmount = advances.Any() ? advances.Average(a => a.Amount) : 0
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على إحصائيات السلف");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// حذف طلب سلفة (فقط إذا كانت معلقة)
        /// </summary>
        /// <param name="id">معرف السلفة</param>
        /// <returns>نتيجة العملية</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> DeleteAdvance(int id)
        {
            try
            {
                var advance = await _context.EmployeeAdvances.FindAsync(id);
                if (advance == null)
                {
                    return NotFound(new { message = "السلفة غير موجودة" });
                }

                if (advance.Status != "pending")
                {
                    return BadRequest(new { message = "لا يمكن حذف السلفة، الحالة الحالية: " + advance.Status });
                }

                _context.EmployeeAdvances.Remove(advance);
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "حذف طلب سلفة",
                    "EmployeeAdvance",
                    id,
                    advance.EmployeeId,
                    $"تم حذف طلب السلفة بمبلغ {advance.Amount:C}"
                );

                return Ok(new { message = "تم حذف طلب السلفة بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في حذف السلفة {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// البحث في السلف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>نتائج البحث</returns>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> SearchAdvances(string searchTerm)
        {
            try
            {
                if (string.IsNullOrEmpty(searchTerm))
                {
                    return Ok(new List<object>());
                }

                var advances = await _context.EmployeeAdvances
                    .Include(a => a.Employee)
                    .Include(a => a.Approver)
                    .Where(a =>
                        a.Employee.Username.Contains(searchTerm) ||
                        a.Employee.FullNameArabic.Contains(searchTerm) ||
                        a.Reason.Contains(searchTerm) ||
                        a.Notes.Contains(searchTerm) ||
                        a.Id.ToString().Contains(searchTerm))
                    .OrderByDescending(a => a.RequestDate)
                    .Take(20)
                    .Select(a => new
                    {
                        a.Id,
                        a.EmployeeId,
                        a.AdvanceType,
                        a.Amount,
                        a.RemainingAmount,
                        a.MonthlyDeduction,
                        a.RequestDate,
                        a.ApprovalDate,
                        a.StartDeductionDate,
                        a.Status,
                        a.Reason,
                        a.ApprovedBy,
                        a.Notes,
                        a.CreatedAt,
                        a.UpdatedAt,
                        EmployeeName = a.Employee.Username,
                        EmployeeNameArabic = a.Employee.FullNameArabic,
                        ApproverName = a.Approver != null ? a.Approver.Username : null
                    })
                    .ToListAsync();

                await _loggingService.LogActivityAsync(
                    "SearchAdvances",
                    "EmployeeAdvance",
                    0,
                    0,
                    $"تم البحث في السلف بالمصطلح: {searchTerm}"
                );

                return Ok(advances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في السلف");
                // await _loggingService.LogSystemAsync("Error", "EmployeeAdvance", ex.Message, null, Request.HttpContext.Connection.RemoteIpAddress?.ToString());
                return StatusCode(500, new { message = "حدث خطأ في البحث", error = ex.Message });
            }
        }

        /// <summary>
        /// التحقق من أهلية الموظف لطلب سلفة جديدة
        /// </summary>
        /// <param name="employeeId">معرف الموظف</param>
        /// <returns>معلومات الأهلية</returns>
        [HttpGet("employee/{employeeId}/eligibility")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> CheckAdvanceEligibility(int employeeId)
        {
            try
            {
                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(employeeId);
                if (employee == null || employee.IsDeleted)
                {
                    return NotFound(new { message = "الموظف غير موجود" });
                }

                // التحقق من السلف الحالية
                var currentAdvances = await _context.EmployeeAdvances
                    .Where(a => a.EmployeeId == employeeId &&
                               (a.Status == "pending" || a.Status == "approved"))
                    .ToListAsync();

                var hasPendingAdvance = currentAdvances.Any(a => a.Status == "pending");
                var hasActiveAdvance = currentAdvances.Any(a => a.Status == "approved" && a.RemainingAmount > 0);
                var totalOutstandingAmount = currentAdvances
                    .Where(a => a.Status == "approved")
                    .Sum(a => a.RemainingAmount);

                // قواعد الأهلية (يمكن تخصيصها) - بالريال اليمني
                var maxAdvanceAmount = 5000000; // الحد الأقصى للسلفة (5 مليون ريال يمني)
                var maxOutstandingAmount = 10000000; // الحد الأقصى للمبلغ المستحق (10 مليون ريال يمني)
                var maxActiveAdvances = 2; // الحد الأقصى للسلف النشطة

                var isEligible = !hasPendingAdvance &&
                                currentAdvances.Count < maxActiveAdvances &&
                                totalOutstandingAmount < maxOutstandingAmount;

                var eligibilityInfo = new
                {
                    IsEligible = isEligible,
                    HasPendingAdvance = hasPendingAdvance,
                    HasActiveAdvance = hasActiveAdvance,
                    ActiveAdvancesCount = currentAdvances.Count(a => a.Status == "approved"),
                    TotalOutstandingAmount = totalOutstandingAmount,
                    MaxAdvanceAmount = maxAdvanceAmount,
                    MaxOutstandingAmount = maxOutstandingAmount,
                    MaxActiveAdvances = maxActiveAdvances,
                    RemainingEligibleAmount = Math.Max(0, maxOutstandingAmount - totalOutstandingAmount),
                    Reasons = new List<string>()
                };

                // إضافة أسباب عدم الأهلية
                var reasons = (List<string>)eligibilityInfo.Reasons;
                if (hasPendingAdvance)
                    reasons.Add("يوجد طلب سلفة في الانتظار");
                if (currentAdvances.Count >= maxActiveAdvances)
                    reasons.Add($"تم الوصول للحد الأقصى من السلف النشطة ({maxActiveAdvances})");
                if (totalOutstandingAmount >= maxOutstandingAmount)
                    reasons.Add($"تم الوصول للحد الأقصى من المبلغ المستحق ({maxOutstandingAmount:C})");

                return Ok(eligibilityInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من أهلية السلفة للموظف {EmployeeId}", employeeId);
                // await _loggingService.LogSystemAsync("Error", "EmployeeAdvance", ex.Message, null, Request.HttpContext.Connection.RemoteIpAddress?.ToString());
                return StatusCode(500, new { message = "حدث خطأ في التحقق من الأهلية", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على تقرير السلف لفترة محددة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="departmentId">معرف القسم</param>
        /// <param name="advanceType">نوع السلفة</param>
        /// <param name="status">حالة السلفة</param>
        /// <returns>تقرير السلف</returns>
        [HttpGet("report")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetAdvanceReport(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int? departmentId = null,
            string? advanceType = null,
            string? status = null)
        {
            try
            {
                var query = _context.EmployeeAdvances
                    .Include(a => a.Employee)
                    .ThenInclude(e => e.Department)
                    .Include(a => a.Approver)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (fromDate.HasValue)
                {
                    query = query.Where(a => a.RequestDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(a => a.RequestDate <= toDate.Value);
                }

                if (departmentId.HasValue)
                {
                    query = query.Where(a => a.Employee.DepartmentId == departmentId.Value);
                }

                if (!string.IsNullOrEmpty(advanceType))
                {
                    query = query.Where(a => a.AdvanceType == advanceType);
                }

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(a => a.Status == status);
                }

                var advances = await query.OrderByDescending(a => a.RequestDate).ToListAsync();

                // إعداد التقرير
                var report = new
                {
                    ReportDate = DateTime.Now,
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalAdvances = advances.Count,
                    TotalAmount = advances.Sum(a => a.Amount),
                    TotalRemainingAmount = advances.Where(a => a.Status == "approved").Sum(a => a.RemainingAmount),
                    TotalPaidAmount = advances.Sum(a => a.Amount - a.RemainingAmount),

                    // إحصائيات حسب الحالة
                    StatusBreakdown = advances.GroupBy(a => a.Status).Select(g => new
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(a => a.Amount)
                    }).ToList(),

                    // إحصائيات حسب النوع
                    TypeBreakdown = advances.GroupBy(a => a.AdvanceType).Select(g => new
                    {
                        AdvanceType = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(a => a.Amount)
                    }).ToList(),

                    // إحصائيات حسب القسم
                    DepartmentBreakdown = advances.GroupBy(a => a.Employee.Department?.Name ?? "غير محدد").Select(g => new
                    {
                        Department = g.Key,
                        Count = g.Count(),
                        TotalAmount = g.Sum(a => a.Amount)
                    }).ToList(),

                    // تفاصيل السلف
                    Advances = advances.Select(a => new
                    {
                        a.Id,
                        a.EmployeeId,
                        EmployeeName = a.Employee.FullNameArabic ?? a.Employee.Username,
                        Department = a.Employee.Department?.Name ?? "غير محدد",
                        a.AdvanceType,
                        a.Amount,
                        a.RemainingAmount,
                        PaidAmount = a.Amount - a.RemainingAmount,
                        a.MonthlyDeduction,
                        a.RequestDate,
                        a.ApprovalDate,
                        a.StartDeductionDate,
                        a.Status,
                        a.Reason,
                        ApproverName = a.Approver?.Username,
                        a.Notes
                    }).ToList()
                };

                await _loggingService.LogActivityAsync(
                    "GetAdvanceReport",
                    "EmployeeAdvance",
                    0,
                    0,
                    $"تم إنشاء تقرير السلف للفترة من {fromDate} إلى {toDate}"
                );

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقرير السلف");
                // await _loggingService.LogSystemAsync("Error", "EmployeeAdvance", ex.Message, null, Request.HttpContext.Connection.RemoteIpAddress?.ToString());
                return StatusCode(500, new { message = "حدث خطأ في إنشاء التقرير", error = ex.Message });
            }
        }
    }

    // DTOs للسلف
    public class CreateAdvanceDto
    {
        [Required(ErrorMessage = "معرف الموظف مطلوب")]
        public int EmployeeId { get; set; }

        [Required(ErrorMessage = "نوع السلفة مطلوب")]
        [StringLength(50, ErrorMessage = "نوع السلفة يجب أن يكون أقل من 50 حرف")]
        public string AdvanceType { get; set; } = string.Empty; // salary_advance, financial_assistance, loan

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(1, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "القسط الشهري يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MonthlyDeduction { get; set; }

        [Required(ErrorMessage = "تاريخ الطلب مطلوب")]
        public DateTime RequestDate { get; set; }

        [StringLength(500, ErrorMessage = "السبب يجب أن يكون أقل من 500 حرف")]
        public string? Reason { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }

    public class ApproveAdvanceDto
    {
        [Required(ErrorMessage = "معرف المعتمد مطلوب")]
        public int ApprovedBy { get; set; }

        [Required(ErrorMessage = "قرار الموافقة مطلوب")]
        public bool IsApproved { get; set; }

        public DateTime? StartDeductionDate { get; set; }

        [StringLength(500, ErrorMessage = "سبب الرفض يجب أن يكون أقل من 500 حرف")]
        public string? RejectionReason { get; set; }
    }

    public class RecordPaymentDto
    {
        [Required(ErrorMessage = "مبلغ الدفعة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدفعة يجب أن يكون أكبر من صفر")]
        public decimal PaymentAmount { get; set; }
    }
}
