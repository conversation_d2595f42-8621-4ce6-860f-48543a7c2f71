-- إنشاء جدول تفاعلات الرسائل (Message Reactions)
-- هذا الجدول يتتبع تفاعلات المستخدمين مع الرسائل (إعجاب، حب، ضحك، إلخ)

USE [TaskManagementDB]
GO

PRINT '🎭 === إنشاء جدول تفاعلات الرسائل ==='

-- 1. التحقق من وجود الجدول وحذفه إذا كان موجوداً (للتطوير فقط)
IF OBJECT_ID('dbo.message_reactions', 'U') IS NOT NULL
BEGIN
    PRINT '⚠️ جدول message_reactions موجود - سيتم حذفه وإعادة إنشاؤه'
    DROP TABLE [dbo].[message_reactions]
END

-- 2. إنشاء جدول message_reactions
PRINT '🔨 إنشاء جدول message_reactions...'
CREATE TABLE [dbo].[message_reactions] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [message_id] INT NOT NULL,
    [user_id] INT NOT NULL,
    [reaction_type] NVARCHAR(50) NOT NULL,
    [created_at] BIGINT NOT NULL,
    [updated_at] BIGINT NULL,
    [is_deleted] BIT NOT NULL DEFAULT 0,
    
    -- إضافة المفتاح الأساسي
    CONSTRAINT [PK_message_reactions] PRIMARY KEY CLUSTERED ([id] ASC),
    
    -- إضافة قيد فريد لمنع تكرار التفاعل (مستخدم واحد لا يمكنه إضافة نفس التفاعل مرتين)
    CONSTRAINT [UQ_message_reactions_message_user_type] UNIQUE ([message_id], [user_id], [reaction_type]),
    
    -- إضافة المفاتيح الخارجية
    CONSTRAINT [FK_message_reactions_message] FOREIGN KEY ([message_id]) 
        REFERENCES [dbo].[messages] ([id]) ON DELETE CASCADE,
    
    CONSTRAINT [FK_message_reactions_user] FOREIGN KEY ([user_id]) 
        REFERENCES [dbo].[users] ([id]) ON DELETE CASCADE
);

-- 3. إنشاء الفهارس لتحسين الأداء
PRINT '📊 إنشاء فهارس الأداء...'

CREATE INDEX [IX_message_reactions_message_id] ON [dbo].[message_reactions] ([message_id]);
CREATE INDEX [IX_message_reactions_user_id] ON [dbo].[message_reactions] ([user_id]);
CREATE INDEX [IX_message_reactions_reaction_type] ON [dbo].[message_reactions] ([reaction_type]);
CREATE INDEX [IX_message_reactions_created_at] ON [dbo].[message_reactions] ([created_at]);
CREATE INDEX [IX_message_reactions_is_deleted] ON [dbo].[message_reactions] ([is_deleted]);

-- فهرس مركب للاستعلامات المتكررة
CREATE INDEX [IX_message_reactions_message_type_deleted] ON [dbo].[message_reactions] 
    ([message_id], [reaction_type], [is_deleted]);

-- 4. إضافة قيود التحقق
PRINT '🔒 إضافة قيود التحقق...'

-- قيد للتحقق من أنواع التفاعلات المسموحة
ALTER TABLE [dbo].[message_reactions]
ADD CONSTRAINT [CK_message_reactions_type] 
CHECK ([reaction_type] IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry', 'thumbs_up', 'thumbs_down', 'heart', 'fire'));

-- 5. إضافة تعليقات على الجدول والأعمدة
PRINT '📝 إضافة التوثيق...'

EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'جدول تفاعلات الرسائل - يحتوي على جميع تفاعلات المستخدمين مع الرسائل',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'message_reactions';

EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'معرف التفاعل الفريد',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'message_reactions',
    @level2type = N'COLUMN', @level2name = N'id';

EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'معرف الرسالة المتفاعل معها',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'message_reactions',
    @level2type = N'COLUMN', @level2name = N'message_id';

EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'معرف المستخدم المتفاعل',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'message_reactions',
    @level2type = N'COLUMN', @level2name = N'user_id';

EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'نوع التفاعل (like, love, laugh, wow, sad, angry, etc.)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'message_reactions',
    @level2type = N'COLUMN', @level2name = N'reaction_type';

-- 6. إدراج بيانات تجريبية (اختيارية)
PRINT '🧪 إدراج بيانات تجريبية...'

-- التحقق من وجود رسائل ومستخدمين للاختبار
DECLARE @TestMessageId INT = (SELECT TOP 1 id FROM messages WHERE is_deleted = 0)
DECLARE @TestUserId INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0)

IF @TestMessageId IS NOT NULL AND @TestUserId IS NOT NULL
BEGIN
    -- إدراج تفاعلات تجريبية
    INSERT INTO [dbo].[message_reactions] (message_id, user_id, reaction_type, created_at)
    VALUES 
        (@TestMessageId, @TestUserId, 'like', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())),
        (@TestMessageId, @TestUserId, 'love', DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()) - 100);
    
    PRINT '✅ تم إدراج بيانات تجريبية'
END
ELSE
BEGIN
    PRINT '⚠️ لم يتم العثور على رسائل أو مستخدمين للاختبار'
END

-- 7. عرض ملخص الجدول
PRINT '📋 === ملخص جدول message_reactions ==='
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'message_reactions'
ORDER BY ORDINAL_POSITION

-- 8. عرض الفهارس المنشأة
PRINT '📊 === الفهارس المنشأة ==='
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    c.name AS column_name
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('message_reactions')
ORDER BY i.name, ic.key_ordinal

-- 9. عرض القيود المنشأة
PRINT '🔒 === القيود المنشأة ==='
SELECT 
    name AS constraint_name,
    type_desc AS constraint_type
FROM sys.objects 
WHERE parent_object_id = OBJECT_ID('message_reactions') 
    AND type IN ('C', 'F', 'PK', 'UQ')
ORDER BY type_desc, name

PRINT '✅ === تم إنشاء جدول message_reactions بنجاح ==='
PRINT 'الجدول جاهز لتتبع تفاعلات المستخدمين مع الرسائل'
PRINT 'أنواع التفاعلات المدعومة: like, love, laugh, wow, sad, angry, thumbs_up, thumbs_down, heart, fire'
